<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\CompetitorMonitoringTask;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Get test user
$user = User::where('email', '<EMAIL>')->first();
if (!$user) {
    echo "Test user not found\n";
    exit(1);
}

// Set authenticated user
Auth::login($user);

// Test CompetitorMonitoringTask query
echo "Testing CompetitorMonitoringTask query...\n";
try {
    $tasks = CompetitorMonitoringTask::with(['user', 'dataSource', 'taskGroup'])
        ->where('user_id', $user->id)
        ->get();
    
    echo "Found " . $tasks->count() . " tasks\n";
    
    foreach ($tasks as $task) {
        echo "Task: " . $task->name . "\n";
        echo "Status: " . $task->status . "\n";
        echo "Data Source: " . ($task->dataSource ? $task->dataSource->name : 'None') . "\n";
        echo "Task Group: " . ($task->taskGroup ? $task->taskGroup->name : 'None') . "\n";
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
