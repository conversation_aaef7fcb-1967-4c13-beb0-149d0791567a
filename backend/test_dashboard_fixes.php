<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\ProductDataController;
use App\Models\ProductData;
use App\Models\Alert;
use App\Models\AlertRule;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试渠道价格监测看板修复 ===\n\n";

// 1. 测试上架状态字段数据
echo "1. 测试上架状态字段数据...\n";
$products = ProductData::take(10)->get(['id', 'item_id', 'title', 'state']);
$stateStats = ['上架' => 0, '下架' => 0, '未知' => 0];

foreach ($products as $product) {
    if ($product->state === 1) {
        $stateStats['上架']++;
    } elseif ($product->state === 0) {
        $stateStats['下架']++;
    } else {
        $stateStats['未知']++;
    }
}

echo "   状态统计:\n";
foreach ($stateStats as $status => $count) {
    echo "     {$status}: {$count} 个商品\n";
}
echo "\n";

// 2. 测试预警类型筛选（验证之前的修复）
echo "2. 测试预警类型筛选功能...\n";
$testTypes = ['promotion_price_deviation', 'channel_price_deviation'];

foreach ($testTypes as $alertType) {
    echo "   测试筛选类型: {$alertType}\n";
    
    $request = new Request();
    $request->merge([
        'alert_type' => $alertType,
        'per_page' => 5
    ]);
    
    $controller = new ProductDataController();
    
    try {
        $response = $controller->index($request);
        $responseData = $response->getData(true);
        
        if ($responseData['success']) {
            $products = $responseData['data']['data'] ?? [];
            echo "     找到 " . count($products) . " 个产品\n";
            
            // 检查返回的产品是否包含state字段
            if (!empty($products)) {
                $firstProduct = $products[0];
                $hasStateField = isset($firstProduct['state']);
                echo "     产品数据包含state字段: " . ($hasStateField ? '是' : '否') . "\n";
                
                if ($hasStateField) {
                    echo "     第一个产品状态: " . ($firstProduct['state'] === 1 ? '上架' : ($firstProduct['state'] === 0 ? '下架' : '未知')) . "\n";
                }
            }
        } else {
            echo "     筛选失败: " . ($responseData['message'] ?? '未知错误') . "\n";
        }
    } catch (\Exception $e) {
        echo "     筛选出错: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 3. 测试预警规则数据结构
echo "3. 测试预警规则数据结构...\n";
$rules = AlertRule::take(5)->get(['id', 'name', 'rule_type', 'description', 'status']);

echo "   预警规则样例:\n";
foreach ($rules as $rule) {
    echo "     规则ID: {$rule->id}\n";
    echo "     规则名称: {$rule->name}\n";
    echo "     规则类型: " . json_encode($rule->rule_type) . "\n";
    echo "     描述: " . ($rule->description ?: '暂无描述') . "\n";
    echo "     状态: " . ($rule->status ? '启用' : '禁用') . "\n";
    echo "     ---\n";
}

echo "\n=== 测试完成 ===\n";
echo "修复内容总结:\n";
echo "1. ✅ 时间控件布局优化 - 避免压住对比按钮\n";
echo "2. ✅ 商品列表添加上架状态字段 - 显示state字段（1=上架，0=下架）\n";
echo "3. ✅ 预警规则列表样式优化 - 更整齐的表格布局\n";
echo "4. ✅ 预警类型筛选功能 - 支持JSON数组查询\n";
