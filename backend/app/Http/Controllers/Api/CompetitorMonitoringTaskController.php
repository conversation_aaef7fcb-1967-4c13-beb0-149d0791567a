<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CompetitorMonitoringTask;
use App\Models\DataSource;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class CompetitorMonitoringTaskController extends Controller
{
    use AuthorizesRequests;

    /**
     * 获取竞品监控任务列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = CompetitorMonitoringTask::with(['user', 'dataSource', 'taskGroup'])
            ->where('user_id', Auth::id());
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 按任务组筛选
        if ($request->has('task_group_id')) {
            $query->where('task_group_id', $request->get('task_group_id'));
        }
        
        // 按数据源筛选
        if ($request->has('data_source_id')) {
            $query->where('data_source_id', $request->get('data_source_id'));
        }
        
        // 按状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $tasks = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $tasks,
            'message' => '竞品监控任务列表获取成功'
        ]);
    }

    /**
     * 创建竞品监控任务
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'task_group_id' => 'nullable|exists:task_groups,id',
            'data_source_id' => 'required|exists:data_sources,id',
            'target_products' => 'required|array',
            'competitor_keywords' => 'nullable|array',
            'monitor_fields' => 'required|array',
            'frequency_type' => 'required|in:interval,cron',
            'frequency_value' => 'nullable|integer|min:1',
            'cron_expression' => 'nullable|string',
            'our_guide_price_min' => 'nullable|numeric|min:0',
            'our_guide_price_max' => 'nullable|numeric|min:0',
            'our_guide_price_avg' => 'nullable|numeric|min:0',
            'alert_rule_ids' => 'nullable|array',
            'alert_rule_ids.*' => 'exists:alert_rules,id',
            'schedule_type' => 'nullable|in:daily,specific_date,weekly',
            'execution_time' => 'nullable|date_format:H:i',
            'execution_dates' => 'nullable|array',
            'execution_weekdays' => 'nullable|array',
            'auto_start' => 'boolean',
            'notify_on_error' => 'boolean',
            'notification_config' => 'nullable|array',
        ]);

        // 验证数据源权限
        $dataSource = DataSource::find($validated['data_source_id']);
        if (!$dataSource || $dataSource->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权使用该数据源'
            ], 403);
        }

        // 验证任务组权限
        if (!empty($validated['task_group_id'])) {
            $taskGroup = TaskGroup::find($validated['task_group_id']);
            if (!$taskGroup || $taskGroup->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权使用该任务组'
                ], 403);
            }
        }

        $validated['user_id'] = Auth::id();
        $validated['status'] = 'pending';

        // 计算下次执行时间
        if ($validated['frequency_type'] === 'interval' && !empty($validated['frequency_value'])) {
            $validated['next_run_at'] = now()->addMinutes($validated['frequency_value']);
        }

        $task = CompetitorMonitoringTask::create($validated);

        return response()->json([
            'success' => true,
            'data' => $task->load(['user', 'dataSource', 'taskGroup']),
            'message' => '竞品监控任务创建成功'
        ], 201);
    }

    /**
     * 获取单个竞品监控任务详情
     */
    public function show(CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务'
            ], 403);
        }

        $task = $competitorMonitoringTask->load(['user', 'dataSource', 'taskGroup']);

        return response()->json([
            'success' => true,
            'data' => $task,
            'message' => '任务详情获取成功'
        ]);
    }

    /**
     * 更新竞品监控任务
     */
    public function update(Request $request, CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改该任务'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:100',
            'description' => 'nullable|string',
            'task_group_id' => 'nullable|exists:task_groups,id',
            'data_source_id' => 'sometimes|required|exists:data_sources,id',
            'target_products' => 'sometimes|required|array',
            'competitor_keywords' => 'nullable|array',
            'monitor_fields' => 'sometimes|required|array',
            'frequency_type' => 'sometimes|required|in:interval,cron',
            'frequency_value' => 'nullable|integer|min:1',
            'cron_expression' => 'nullable|string',
            'our_guide_price_min' => 'nullable|numeric|min:0',
            'our_guide_price_max' => 'nullable|numeric|min:0',
            'our_guide_price_avg' => 'nullable|numeric|min:0',
            'alert_rule_ids' => 'nullable|array',
            'alert_rule_ids.*' => 'exists:alert_rules,id',
            'schedule_type' => 'nullable|in:daily,specific_date,weekly',
            'execution_time' => 'nullable|date_format:H:i',
            'execution_dates' => 'nullable|array',
            'execution_weekdays' => 'nullable|array',
            'auto_start' => 'boolean',
            'notify_on_error' => 'boolean',
            'notification_config' => 'nullable|array',
        ]);

        // 验证数据源权限
        if (isset($validated['data_source_id'])) {
            $dataSource = DataSource::find($validated['data_source_id']);
            if (!$dataSource || $dataSource->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权使用该数据源'
                ], 403);
            }
        }

        // 验证任务组权限
        if (isset($validated['task_group_id']) && !empty($validated['task_group_id'])) {
            $taskGroup = TaskGroup::find($validated['task_group_id']);
            if (!$taskGroup || $taskGroup->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权使用该任务组'
                ], 403);
            }
        }

        // 如果修改了频率设置，重新计算下次执行时间
        if (isset($validated['frequency_type']) || isset($validated['frequency_value'])) {
            $frequencyType = $validated['frequency_type'] ?? $competitorMonitoringTask->frequency_type;
            $frequencyValue = $validated['frequency_value'] ?? $competitorMonitoringTask->frequency_value;
            
            if ($frequencyType === 'interval' && !empty($frequencyValue)) {
                $validated['next_run_at'] = now()->addMinutes($frequencyValue);
            }
        }

        $competitorMonitoringTask->update($validated);

        return response()->json([
            'success' => true,
            'data' => $competitorMonitoringTask->load(['user', 'dataSource', 'taskGroup']),
            'message' => '任务更新成功'
        ]);
    }

    /**
     * 删除竞品监控任务
     */
    public function destroy(CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权删除该任务'
            ], 403);
        }

        $competitorMonitoringTask->delete();

        return response()->json([
            'success' => true,
            'message' => '任务删除成功'
        ]);
    }

    /**
     * 启动任务
     */
    public function start(CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作该任务'
            ], 403);
        }

        if (!$competitorMonitoringTask->canRun()) {
            return response()->json([
                'success' => false,
                'message' => '任务配置不完整，无法启动'
            ], 422);
        }

        $competitorMonitoringTask->update([
            'status' => 'running',
            'next_run_at' => $competitorMonitoringTask->calculateNextRunTime()
        ]);

        return response()->json([
            'success' => true,
            'data' => $competitorMonitoringTask,
            'message' => '任务启动成功'
        ]);
    }

    /**
     * 暂停任务
     */
    public function pause(CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作该任务'
            ], 403);
        }

        $competitorMonitoringTask->update([
            'status' => 'paused',
            'next_run_at' => null
        ]);

        return response()->json([
            'success' => true,
            'data' => $competitorMonitoringTask,
            'message' => '任务暂停成功'
        ]);
    }

    /**
     * 立即执行一次任务（手动触发）
     */
    public function runNow(CompetitorMonitoringTask $competitorMonitoringTask): JsonResponse
    {
        // 权限检查
        if ($competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作该任务'
            ], 403);
        }

        if (!$competitorMonitoringTask->data_source_id) {
            return response()->json([
                'success' => false,
                'message' => '任务未配置数据源，无法执行',
            ], 422);
        }

        // 调度队列任务立即执行
        \App\Jobs\ProcessCompetitorDataSourceTask::dispatch(
            $competitorMonitoringTask->data_source_id,
            $competitorMonitoringTask->target_products ?? [],
            [],
            $competitorMonitoringTask->id
        );

        // 更新 last_run_at 立即展示
        $competitorMonitoringTask->update([
            'last_run_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => '任务已提交执行',
        ]);
    }
}
