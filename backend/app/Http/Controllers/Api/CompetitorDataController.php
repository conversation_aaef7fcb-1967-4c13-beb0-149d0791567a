<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CompetitorMonitoringTask;
use App\Models\CompetitorProductData;
use App\Models\CompetitorProductSku;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CompetitorDataController extends Controller
{
    /**
     * 获取竞品数据列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = CompetitorProductData::with(['competitorMonitoringTask', 'competitorProductSkus'])
            ->whereHas('competitorMonitoringTask', function ($q) {
                $q->where('user_id', Auth::id());
            });

        // 按任务筛选
        if ($request->has('task_id')) {
            $query->where('competitor_monitoring_task_id', $request->get('task_id'));
        }

        // 按商品状态筛选
        if ($request->has('state')) {
            $query->where('state', $request->get('state'));
        }

        // 按类目筛选
        if ($request->has('category_id')) {
            $query->where('category_id', $request->get('category_id'));
        }

        // 按店铺筛选
        if ($request->has('shop_id')) {
            $query->where('shop_id', $request->get('shop_id'));
        }

        // 搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('shop_name', 'like', "%{$search}%");
            });
        }

        // 排序
        $sortBy = $request->get('sort_by', 'last_collected_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // 分页
        $perPage = $request->get('per_page', 15);
        $products = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $products,
            'message' => '竞品数据列表获取成功'
        ]);
    }

    /**
     * 获取竞品数据详情
     */
    public function show(CompetitorProductData $competitorProductData): JsonResponse
    {
        // 权限检查
        if ($competitorProductData->competitorMonitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该数据'
            ], 403);
        }

        $product = $competitorProductData->load([
            'competitorMonitoringTask',
            'competitorProductSkus'
        ]);

        return response()->json([
            'success' => true,
            'data' => $product,
            'message' => '竞品数据详情获取成功'
        ]);
    }

    /**
     * 获取竞品核心指标统计
     */
    public function getMetrics(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '请指定任务ID'
            ], 422);
        }

        // 权限检查
        $task = CompetitorMonitoringTask::find($taskId);
        if (!$task || $task->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务数据'
            ], 403);
        }

        // 获取任务下的所有商品数据
        $products = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)->get();
        
        if ($products->isEmpty()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'promotion_strategy_tendency' => 0,
                    'comprehensive_discount_rate' => 0,
                    'overall_promotion_intensity' => 0,
                    'comprehensive_deviation_rate' => 0,
                    'min_price_deviation_rate' => 0,
                    'max_price_deviation_rate' => 0,
                    'price_trend_slope' => 0,
                ],
                'message' => '暂无数据'
            ]);
        }

        $metrics = [
            'promotion_strategy_tendency' => 0,
            'comprehensive_discount_rate' => 0,
            'overall_promotion_intensity' => 0,
            'comprehensive_deviation_rate' => 0,
            'min_price_deviation_rate' => 0,
            'max_price_deviation_rate' => 0,
            'price_trend_slope' => 0,
        ];

        // 计算各项指标的平均值
        foreach ($products as $product) {
            $metrics['promotion_strategy_tendency'] += $product->calculatePromotionStrategyTendency();
            $metrics['comprehensive_discount_rate'] += $product->calculateComprehensiveDiscountRate();
            $metrics['overall_promotion_intensity'] += $product->calculateOverallPromotionIntensity();
            $metrics['comprehensive_deviation_rate'] += $product->calculateComprehensiveDeviationRate();
            $metrics['min_price_deviation_rate'] += $product->calculateMinPriceDeviationRate();
            $metrics['max_price_deviation_rate'] += $product->calculateMaxPriceDeviationRate();
            $metrics['price_trend_slope'] += $product->calculatePriceTrendSlope();
        }

        $productCount = $products->count();
        foreach ($metrics as $key => $value) {
            $metrics[$key] = round($value / $productCount, 4);
        }

        return response()->json([
            'success' => true,
            'data' => $metrics,
            'message' => '竞品核心指标获取成功'
        ]);
    }

    /**
     * 获取竞品价格趋势数据
     */
    public function getPriceTrends(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $days = $request->get('days', 30);
        
        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '请指定任务ID'
            ], 422);
        }

        // 权限检查
        $task = CompetitorMonitoringTask::find($taskId);
        if (!$task || $task->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务数据'
            ], 403);
        }

        // 获取价格趋势数据
        $trends = DB::table('competitor_product_data_history as h')
            ->join('competitor_product_data as p', function ($join) use ($taskId) {
                $join->on('h.competitor_monitoring_task_id', '=', 'p.competitor_monitoring_task_id')
                     ->on('h.item_id', '=', 'p.item_id')
                     ->where('p.competitor_monitoring_task_id', $taskId);
            })
            ->where('h.collected_at', '>=', now()->subDays($days))
            ->select([
                DB::raw('DATE(h.collected_at) as date'),
                DB::raw('AVG(h.min_hand_price) as avg_min_price'),
                DB::raw('AVG(h.max_hand_price) as avg_max_price'),
                DB::raw('AVG((h.min_hand_price + h.max_hand_price) / 2) as avg_price')
            ])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $trends,
            'message' => '价格趋势数据获取成功'
        ]);
    }

    /**
     * 获取促销类型分布
     */
    public function getPromotionDistribution(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '请指定任务ID'
            ], 422);
        }

        // 权限检查
        $task = CompetitorMonitoringTask::find($taskId);
        if (!$task || $task->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务数据'
            ], 403);
        }

        // 获取促销类型分布
        $skus = CompetitorProductSku::whereHas('competitorProductData', function ($q) use ($taskId) {
                $q->where('competitor_monitoring_task_id', $taskId);
            })
            ->whereNotNull('promotion')
            ->get();

        $distribution = [];
        foreach ($skus as $sku) {
            $promotions = $sku->promotion ?? [];
            foreach ($promotions as $promo) {
                $type = $promo['content'] ?? '其他';
                $distribution[$type] = ($distribution[$type] ?? 0) + 1;
            }
        }

        // 转换为图表数据格式
        $chartData = [];
        foreach ($distribution as $type => $count) {
            $chartData[] = [
                'name' => $type,
                'value' => $count
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $chartData,
            'message' => '促销类型分布获取成功'
        ]);
    }

    /**
     * 获取类目价格分布热力图数据
     */
    public function getCategoryPriceHeatmap(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '请指定任务ID'
            ], 422);
        }

        // 权限检查
        $task = CompetitorMonitoringTask::find($taskId);
        if (!$task || $task->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务数据'
            ], 403);
        }

        // 获取类目价格分布
        $heatmapData = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
            ->whereNotNull('category_path')
            ->whereNotNull('min_hand_price')
            ->select([
                'category_path',
                DB::raw('AVG((min_hand_price + max_hand_price) / 2) as avg_price'),
                DB::raw('COUNT(*) as product_count')
            ])
            ->groupBy('category_path')
            ->orderBy('avg_price', 'desc')
            ->limit(20) // 限制显示前20个类目
            ->get();

        return response()->json([
            'success' => true,
            'data' => $heatmapData,
            'message' => '类目价格分布获取成功'
        ]);
    }

    /**
     * 批量更新竞品核心指标
     */
    public function updateMetrics(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        
        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '请指定任务ID'
            ], 422);
        }

        // 权限检查
        $task = CompetitorMonitoringTask::find($taskId);
        if (!$task || $task->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作该任务数据'
            ], 403);
        }

        // 获取任务下的所有商品数据
        $products = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)->get();
        
        foreach ($products as $product) {
            $product->updateAllMetrics();
            
            // 更新SKU级别的指标
            foreach ($product->competitorProductSkus as $sku) {
                $sku->updateAllMetrics();
            }
        }

        return response()->json([
            'success' => true,
            'message' => '竞品核心指标更新成功'
        ]);
    }
}
