<?php

namespace App\Http\Controllers;

use App\Models\CompetitorMonitoringTask;
use App\Models\CompetitorProductData;
use App\Models\CompetitorProductSku;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class CompetitorAnalyticsController extends Controller
{
    protected $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * 获取竞品核心指标概览
     */
    public function getCoreMetrics(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $dateRange = $request->get('date_range', 30);

        $task = CompetitorMonitoringTask::findOrFail($taskId);
        
        // 获取任务下的所有产品数据
        $productData = $task->competitorProductData()
            ->with('competitorProductSkus')
            ->get();

        $metrics = [
            'promotion_strategy_tendency' => $this->calculatePromotionStrategyTendency($productData),
            'comprehensive_discount_rate' => $this->calculateAverageComprehensiveDiscountRate($productData),
            'overall_promotion_intensity' => $this->calculateAveragePromotionIntensity($productData),
            'comprehensive_deviation_rate' => $this->calculateAverageDeviationRate($productData),
            'min_price_deviation_rate' => $this->calculateAverageMinPriceDeviation($productData),
            'max_price_deviation_rate' => $this->calculateAverageMaxPriceDeviation($productData),
            'price_trend_slope' => $this->calculateAveragePriceTrendSlope($productData, $dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $metrics
        ]);
    }

    /**
     * 获取促销策略倾向分析
     */
    public function getPromotionStrategyTendency(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $dateRange = $request->get('date_range', 30);
        $categoryId = $request->get('category_id');

        $query = CompetitorProductData::where('competitor_monitoring_task_id', $taskId);
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($dateRange) {
            $query->where('last_collected_at', '>=', Carbon::now()->subDays($dateRange));
        }

        $productData = $query->get();
        $tendency = $this->calculatePromotionStrategyTendency($productData);

        return response()->json([
            'success' => true,
            'data' => $tendency
        ]);
    }

    /**
     * 获取单品价格综合折扣率
     */
    public function getComprehensiveDiscountRate(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $itemId = $request->get('item_id');

        $productData = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
            ->where('item_id', $itemId)
            ->with('competitorProductSkus')
            ->first();

        if (!$productData) {
            return response()->json([
                'success' => false,
                'message' => '未找到指定商品数据'
            ], 404);
        }

        $discountRate = $productData->calculateComprehensiveDiscountRate();

        return response()->json([
            'success' => true,
            'data' => [
                'item_id' => $itemId,
                'comprehensive_discount_rate' => $discountRate,
                'sku_details' => $productData->competitorProductSkus->map(function ($sku) {
                    return [
                        'sku_id' => $sku->sku_id,
                        'name' => $sku->name,
                        'price' => $sku->price,
                        'sub_price' => $sku->sub_price,
                        'discount_rate' => $sku->calculateComprehensiveDiscountRate()
                    ];
                })
            ]
        ]);
    }

    /**
     * 获取总体促销强度指数
     */
    public function getOverallPromotionIntensity(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $dateRange = $request->get('date_range', 30);

        $query = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
            ->with('competitorProductSkus');

        if ($dateRange) {
            $query->where('last_collected_at', '>=', Carbon::now()->subDays($dateRange));
        }

        $productData = $query->get();
        $intensity = $this->calculateAveragePromotionIntensity($productData);

        return response()->json([
            'success' => true,
            'data' => [
                'overall_promotion_intensity' => $intensity,
                'product_details' => $productData->map(function ($product) {
                    return [
                        'item_id' => $product->item_id,
                        'title' => $product->title,
                        'promotion_intensity' => $product->calculateOverallPromotionIntensity()
                    ];
                })
            ]
        ]);
    }

    /**
     * 获取价格趋势图表数据
     */
    public function getPriceTrendChart(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $itemIds = $request->get('item_ids', []);
        $period = $request->get('period', 30);

        if (empty($itemIds)) {
            // 如果没有指定商品ID，获取任务下的所有商品
            $itemIds = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
                ->pluck('item_id')
                ->toArray();
        }

        $chartData = [];
        foreach ($itemIds as $itemId) {
            $productData = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
                ->where('item_id', $itemId)
                ->first();

            if ($productData) {
                $priceHistory = $productData->getPriceHistory($period);
                $chartData[] = [
                    'item_id' => $itemId,
                    'title' => $productData->title,
                    'price_history' => $priceHistory,
                    'trend_slope' => $productData->calculatePriceTrendSlope($period)
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $chartData
        ]);
    }

    /**
     * 批量计算竞品核心指标
     */
    public function batchCalculateMetrics(Request $request): JsonResponse
    {
        $taskIds = $request->get('task_ids', []);
        $itemIds = $request->get('item_ids', []);
        $forceRecalculate = $request->get('force_recalculate', false);

        $results = [];

        foreach ($taskIds as $taskId) {
            $query = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
                ->with('competitorProductSkus');

            if (!empty($itemIds)) {
                $query->whereIn('item_id', $itemIds);
            }

            $productData = $query->get();

            foreach ($productData as $product) {
                // 更新产品级指标
                $product->calculatePromotionStrategyTendency();
                
                // 更新SKU级指标
                foreach ($product->competitorProductSkus as $sku) {
                    if ($forceRecalculate || !$sku->promotion_strategy_score) {
                        $sku->updateAllMetrics();
                    }
                }

                $results[] = [
                    'task_id' => $taskId,
                    'item_id' => $product->item_id,
                    'title' => $product->title,
                    'metrics_updated' => true,
                    'sku_count' => $product->competitorProductSkus->count()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => '批量计算完成',
            'data' => $results
        ]);
    }

    /**
     * 获取竞品对比分析
     */
    public function getCompetitorComparison(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $itemIds = $request->get('item_ids', []);
        $metrics = $request->get('metrics', [
            'comprehensive_discount_rate',
            'overall_promotion_intensity',
            'comprehensive_deviation_rate'
        ]);

        $comparison = [];
        foreach ($itemIds as $itemId) {
            $productData = CompetitorProductData::where('competitor_monitoring_task_id', $taskId)
                ->where('item_id', $itemId)
                ->with('competitorProductSkus')
                ->first();

            if ($productData) {
                $productMetrics = [
                    'item_id' => $itemId,
                    'title' => $productData->title,
                    'shop_name' => $productData->shop_name,
                ];

                foreach ($metrics as $metric) {
                    switch ($metric) {
                        case 'comprehensive_discount_rate':
                            $productMetrics[$metric] = $productData->calculateComprehensiveDiscountRate();
                            break;
                        case 'overall_promotion_intensity':
                            $productMetrics[$metric] = $productData->calculateOverallPromotionIntensity();
                            break;
                        case 'comprehensive_deviation_rate':
                            $productMetrics[$metric] = $productData->calculateComprehensiveDeviationRate();
                            break;
                        case 'min_price_deviation_rate':
                            $productMetrics[$metric] = $productData->calculateMinPriceDeviationRate();
                            break;
                        case 'max_price_deviation_rate':
                            $productMetrics[$metric] = $productData->calculateMaxPriceDeviationRate();
                            break;
                        case 'price_trend_slope':
                            $productMetrics[$metric] = $productData->calculatePriceTrendSlope();
                            break;
                    }
                }

                $comparison[] = $productMetrics;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $comparison
        ]);
    }

    /**
     * 计算促销策略倾向
     */
    private function calculatePromotionStrategyTendency($productData): array
    {
        $allStrategies = [];
        
        foreach ($productData as $product) {
            $strategies = $product->calculatePromotionStrategyTendency();
            foreach ($strategies as $strategy => $data) {
                if (!isset($allStrategies[$strategy])) {
                    $allStrategies[$strategy] = [
                        'count' => 0,
                        'examples' => []
                    ];
                }
                $allStrategies[$strategy]['count'] += $data['count'];
                $allStrategies[$strategy]['examples'] = array_unique(
                    array_merge($allStrategies[$strategy]['examples'], $data['examples'])
                );
            }
        }

        // 计算总体占比
        $total = array_sum(array_column($allStrategies, 'count'));
        foreach ($allStrategies as $key => &$strategy) {
            $strategy['percentage'] = $total > 0 ? ($strategy['count'] / $total) * 100 : 0;
        }

        return $allStrategies;
    }

    /**
     * 计算平均综合折扣率
     */
    private function calculateAverageComprehensiveDiscountRate($productData): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalRate = $productData->sum(function ($product) {
            return $product->calculateComprehensiveDiscountRate();
        });

        return $totalRate / $productData->count();
    }

    /**
     * 计算平均促销强度
     */
    private function calculateAveragePromotionIntensity($productData): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalIntensity = $productData->sum(function ($product) {
            return $product->calculateOverallPromotionIntensity();
        });

        return $totalIntensity / $productData->count();
    }

    /**
     * 计算平均偏差率
     */
    private function calculateAverageDeviationRate($productData): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalRate = $productData->sum(function ($product) {
            return $product->calculateComprehensiveDeviationRate();
        });

        return $totalRate / $productData->count();
    }

    /**
     * 计算平均最低价偏差率
     */
    private function calculateAverageMinPriceDeviation($productData): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalRate = $productData->sum(function ($product) {
            return $product->calculateMinPriceDeviationRate();
        });

        return $totalRate / $productData->count();
    }

    /**
     * 计算平均最高价偏差率
     */
    private function calculateAverageMaxPriceDeviation($productData): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalRate = $productData->sum(function ($product) {
            return $product->calculateMaxPriceDeviationRate();
        });

        return $totalRate / $productData->count();
    }

    /**
     * 计算平均价格趋势斜率
     */
    private function calculateAveragePriceTrendSlope($productData, int $days = 30): float
    {
        if ($productData->isEmpty()) {
            return 0.0;
        }

        $totalSlope = $productData->sum(function ($product) use ($days) {
            return $product->calculatePriceTrendSlope($days);
        });

        return $totalSlope / $productData->count();
    }

    /**
     * 获取促销类型分布数据
     */
    public function getPromotionDistribution(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $dateRange = $request->get('date_range', 30);

        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '任务ID不能为空'
            ], 400);
        }

        try {
            // 获取指定时间范围内的促销数据
            $endDate = now();
            $startDate = now()->subDays($dateRange);

            $promotionData = CompetitorProductData::where('task_id', $taskId)
                ->whereBetween('collected_at', [$startDate, $endDate])
                ->whereNotNull('promotion_type')
                ->selectRaw('promotion_type, COUNT(*) as count')
                ->groupBy('promotion_type')
                ->get();

            $totalCount = $promotionData->sum('count');

            $distributionData = [];
            foreach ($promotionData as $item) {
                $distributionData[$item->promotion_type] = [
                    'count' => $item->count,
                    'percentage' => $totalCount > 0 ? round(($item->count / $totalCount) * 100, 2) : 0
                ];
            }

            // 如果没有促销数据，添加默认的"无促销"类型
            if (empty($distributionData)) {
                $distributionData['无促销'] = [
                    'count' => 0,
                    'percentage' => 100
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $distributionData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取促销分布数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取类目价格带分布热力图数据
     */
    public function getCategoryPriceHeatmap(Request $request): JsonResponse
    {
        $taskId = $request->get('task_id');
        $categoryId = $request->get('category_id');
        $dateRange = $request->get('date_range', 30);

        if (!$taskId) {
            return response()->json([
                'success' => false,
                'message' => '任务ID不能为空'
            ], 400);
        }

        try {
            $endDate = now();
            $startDate = now()->subDays($dateRange);

            $query = CompetitorProductData::where('task_id', $taskId)
                ->whereBetween('collected_at', [$startDate, $endDate])
                ->whereNotNull('competitor_price');

            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }

            $productData = $query->get();

            // 定义价格区间
            $priceRanges = [
                '0-50', '50-100', '100-200', '200-500',
                '500-1000', '1000-2000', '2000-5000', '5000+'
            ];

            // 获取所有类目
            $categories = $productData->pluck('category_name')->unique()->values()->toArray();
            if (empty($categories)) {
                $categories = ['未分类'];
            }

            // 初始化热力图数据
            $heatmapData = [];

            foreach ($productData as $product) {
                $categoryIndex = array_search($product->category_name ?: '未分类', $categories);
                $priceRangeIndex = $this->getPriceRangeIndex($product->competitor_price, $priceRanges);

                $key = $categoryIndex . '_' . $priceRangeIndex;
                if (!isset($heatmapData[$key])) {
                    $heatmapData[$key] = [$categoryIndex, $priceRangeIndex, 0];
                }
                $heatmapData[$key][2]++;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'heatmap_data' => array_values($heatmapData),
                    'categories' => $categories,
                    'price_ranges' => $priceRanges
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取类目热力图数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取价格区间索引
     */
    private function getPriceRangeIndex($price, $priceRanges): int
    {
        if ($price < 50) return 0;
        if ($price < 100) return 1;
        if ($price < 200) return 2;
        if ($price < 500) return 3;
        if ($price < 1000) return 4;
        if ($price < 2000) return 5;
        if ($price < 5000) return 6;
        return 7; // 5000+
    }
}
