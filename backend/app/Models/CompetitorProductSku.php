<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompetitorProductSku extends Model
{
    use HasFactory;

    protected $fillable = [
        'competitor_product_data_id',
        'sku_id',
        'name',
        'price',
        'sub_price',
        'sub_price_title',
        'quantity',
        'promotion',
        'our_guide_price',
        'promotion_strategy_score',
        'comprehensive_discount_rate',
        'overall_promotion_intensity',
        'comprehensive_deviation_rate',
        'min_price_deviation_rate',
        'max_price_deviation_rate',
        'price_trend_slope',
    ];

    protected $casts = [
        'promotion' => 'json',
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'our_guide_price' => 'decimal:2',
        'promotion_strategy_score' => 'decimal:4',
        'comprehensive_discount_rate' => 'decimal:4',
        'overall_promotion_intensity' => 'decimal:4',
        'comprehensive_deviation_rate' => 'decimal:4',
        'min_price_deviation_rate' => 'decimal:4',
        'max_price_deviation_rate' => 'decimal:4',
        'price_trend_slope' => 'decimal:4',
        'quantity' => 'integer',
    ];

    /**
     * 获取竞品商品数据
     */
    public function competitorProductData(): BelongsTo
    {
        return $this->belongsTo(CompetitorProductData::class);
    }

    /**
     * 计算促销策略倾向得分
     */
    public function calculatePromotionStrategyScore(): float
    {
        $promotions = $this->promotion ?? [];
        $score = 0.0;
        
        foreach ($promotions as $promo) {
            $content = $promo['content'] ?? '';
            
            // 根据促销类型给予不同权重
            switch (strtolower($content)) {
                case '满减':
                case 'full_reduction':
                    $score += 3.0;
                    break;
                case '折扣':
                case 'discount':
                    $score += 2.5;
                    break;
                case '优惠券':
                case 'coupon':
                    $score += 2.0;
                    break;
                case '赠品':
                case 'gift':
                    $score += 1.5;
                    break;
                default:
                    $score += 1.0;
                    break;
            }
        }
        
        return $score;
    }

    /**
     * 计算单品价格综合折扣率（SKU级）
     */
    public function calculateComprehensiveDiscountRate(): float
    {
        if (!$this->price || $this->price <= 0) {
            return 0.0;
        }
        
        $subPrice = $this->sub_price ?? $this->price;
        
        return (1 - ($subPrice / $this->price)) * 100;
    }

    /**
     * 计算总体促销强度指数（SKU级）
     */
    public function calculateOverallPromotionIntensity(): float
    {
        // 对于单个SKU，促销强度主要看折扣程度
        $discountRate = $this->calculateComprehensiveDiscountRate() / 100;
        
        // 如果有促销信息，增加权重
        $promotionWeight = empty($this->promotion) ? 0.5 : 1.0;
        
        return $discountRate * $promotionWeight;
    }

    /**
     * 计算单品价格综合偏差率（SKU级）
     */
    public function calculateComprehensiveDeviationRate(): float
    {
        if (!$this->our_guide_price || $this->our_guide_price <= 0) {
            return 0.0;
        }
        
        $subPrice = $this->sub_price ?? $this->price ?? 0;
        
        return (($this->our_guide_price - $subPrice) / $this->our_guide_price) * 100;
    }

    /**
     * 计算单品最低价偏差率（SKU级）
     */
    public function calculateMinPriceDeviationRate(): float
    {
        $productData = $this->competitorProductData;
        $task = $productData->competitorMonitoringTask ?? null;
        
        if (!$task || !$task->our_guide_price_min || $task->our_guide_price_min <= 0) {
            return 0.0;
        }
        
        $subPrice = $this->sub_price ?? $this->price ?? 0;
        
        return (($task->our_guide_price_min - $subPrice) / $task->our_guide_price_min) * 100;
    }

    /**
     * 计算单品最高价偏差率（SKU级）
     */
    public function calculateMaxPriceDeviationRate(): float
    {
        $productData = $this->competitorProductData;
        $task = $productData->competitorMonitoringTask ?? null;
        
        if (!$task || !$task->our_guide_price_max || $task->our_guide_price_max <= 0) {
            return 0.0;
        }
        
        $subPrice = $this->sub_price ?? $this->price ?? 0;
        
        return (($task->our_guide_price_max - $subPrice) / $task->our_guide_price_max) * 100;
    }

    /**
     * 获取SKU价格历史数据
     */
    public function getPriceHistory(int $days = 30): array
    {
        $productData = $this->competitorProductData;
        
        $history = CompetitorProductSkuHistory::whereHas('competitorProductDataHistory', function ($query) use ($productData) {
                $query->where('competitor_monitoring_task_id', $productData->competitor_monitoring_task_id)
                      ->where('item_id', $productData->item_id);
            })
            ->where('sku_id', $this->sku_id)
            ->whereHas('competitorProductDataHistory', function ($query) use ($days) {
                $query->where('collected_at', '>=', now()->subDays($days));
            })
            ->with('competitorProductDataHistory')
            ->orderBy('created_at')
            ->get();
        
        return $history->map(function ($item, $index) {
            return [
                'day' => $index + 1,
                'price' => $item->sub_price ?? $item->price ?? 0
            ];
        })->toArray();
    }

    /**
     * 计算竞品价格趋势斜率（SKU级）
     */
    public function calculatePriceTrendSlope(int $days = 30): float
    {
        $priceHistory = $this->getPriceHistory($days);
        
        if (count($priceHistory) < 2) {
            return 0.0;
        }
        
        $n = count($priceHistory);
        $sumDay = array_sum(array_column($priceHistory, 'day'));
        $sumPrice = array_sum(array_column($priceHistory, 'price'));
        $sumDayPrice = 0;
        $sumDaySquare = 0;
        
        foreach ($priceHistory as $data) {
            $sumDayPrice += $data['day'] * $data['price'];
            $sumDaySquare += $data['day'] * $data['day'];
        }
        
        $denominator = $n * $sumDaySquare - ($sumDay * $sumDay);
        
        if ($denominator == 0) {
            return 0.0;
        }
        
        $slope = ($n * $sumDayPrice - $sumDay * $sumPrice) / $denominator;
        
        return round($slope, 4);
    }

    /**
     * 更新所有核心指标
     */
    public function updateAllMetrics(): void
    {
        $this->promotion_strategy_score = $this->calculatePromotionStrategyScore();
        $this->comprehensive_discount_rate = $this->calculateComprehensiveDiscountRate();
        $this->overall_promotion_intensity = $this->calculateOverallPromotionIntensity();
        $this->comprehensive_deviation_rate = $this->calculateComprehensiveDeviationRate();
        $this->min_price_deviation_rate = $this->calculateMinPriceDeviationRate();
        $this->max_price_deviation_rate = $this->calculateMaxPriceDeviationRate();
        $this->price_trend_slope = $this->calculatePriceTrendSlope();
        
        $this->save();
    }
}
