<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CompetitorMonitoringTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'user_id',
        'task_group_id',
        'data_source_id',
        'target_products',
        'competitor_keywords',
        'monitor_fields',
        'frequency_type',
        'frequency_value',
        'cron_expression',
        'our_guide_price_min',
        'our_guide_price_max',
        'our_guide_price_avg',
        'status',
        'last_run_at',
        'next_run_at',
        'run_count',
        'success_count',
        'failed_count',
        'last_error',
        'total_products',
        'active_products',
        'alert_count',
        'alert_rule_ids',
        'schedule_type',
        'execution_time',
        'execution_dates',
        'execution_weekdays',
        'auto_start',
        'notify_on_error',
        'notification_config',
    ];

    protected $casts = [
        'target_products' => 'array',
        'competitor_keywords' => 'array',
        'monitor_fields' => 'array',
        'alert_rule_ids' => 'array',
        'execution_dates' => 'array',
        'execution_weekdays' => 'array',
        'notification_config' => 'array',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'our_guide_price_min' => 'decimal:2',
        'our_guide_price_max' => 'decimal:2',
        'our_guide_price_avg' => 'decimal:2',
        'auto_start' => 'boolean',
        'notify_on_error' => 'boolean',
    ];

    /**
     * 获取创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取任务分组
     */
    public function taskGroup(): BelongsTo
    {
        return $this->belongsTo(TaskGroup::class);
    }

    /**
     * 获取数据源
     */
    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    /**
     * 获取竞品商品数据
     */
    public function competitorProductData(): HasMany
    {
        return $this->hasMany(CompetitorProductData::class);
    }

    /**
     * 获取竞品商品数据历史
     */
    public function competitorProductDataHistory(): HasMany
    {
        return $this->hasMany(CompetitorProductDataHistory::class);
    }

    /**
     * 获取预警规则
     */
    public function alertRules()
    {
        if (empty($this->alert_rule_ids)) {
            return collect();
        }
        
        return AlertRule::whereIn('id', $this->alert_rule_ids)->get();
    }

    /**
     * 检查任务是否可以运行
     */
    public function canRun(): bool
    {
        return in_array($this->status, ['pending', 'running']) && 
               $this->data_source_id && 
               !empty($this->target_products);
    }

    /**
     * 获取下次执行时间
     */
    public function calculateNextRunTime(): ?string
    {
        if ($this->frequency_type === 'cron' && $this->cron_expression) {
            // 这里可以使用 cron 表达式解析库来计算下次执行时间
            // 暂时返回 null，后续可以集成 cron 解析库
            return null;
        }

        if ($this->frequency_type === 'interval' && $this->frequency_value) {
            return now()->addMinutes($this->frequency_value)->toDateTimeString();
        }

        return null;
    }

    /**
     * 更新任务统计信息
     */
    public function updateStatistics(): void
    {
        $this->total_products = $this->competitorProductData()->count();
        $this->active_products = $this->competitorProductData()->where('state', 1)->count();
        $this->save();
    }

    /**
     * 记录执行结果
     */
    public function recordExecution(bool $success, ?string $error = null): void
    {
        $this->run_count++;
        
        if ($success) {
            $this->success_count++;
            $this->last_error = null;
        } else {
            $this->failed_count++;
            $this->last_error = $error;
        }
        
        $this->last_run_at = now();
        $this->next_run_at = $this->calculateNextRunTime();
        $this->save();
    }
}
