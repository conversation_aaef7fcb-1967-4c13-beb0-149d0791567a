<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CompetitorProductDataHistory extends Model
{
    use HasFactory;

    protected $table = 'competitor_product_data_history';

    protected $fillable = [
        'competitor_monitoring_task_id',
        'item_id',
        'standardized_data',
        'collected_at',
        'code',
        'title',
        'product_image',
        'price',
        'lowest_price',
        'highest_price',
        'min_hand_price',
        'max_hand_price',
        'stock',
        'sales',
        'comment_count',
        'state',
        'has_sku',
        'item_type',
        'category_id',
        'category_path',
        'shop_id',
        'shop_name',
        'props',
        'promotion',
        'delivery_location',
        'product_url',
        'our_guide_price',
        'collection_batch_id',
    ];

    protected $casts = [
        'standardized_data' => 'array',
        'props' => 'json',
        'promotion' => 'json',
        'collected_at' => 'datetime',
        'price' => 'decimal:2',
        'lowest_price' => 'decimal:2',
        'highest_price' => 'decimal:2',
        'min_hand_price' => 'decimal:2',
        'max_hand_price' => 'decimal:2',
        'our_guide_price' => 'decimal:2',
        'stock' => 'integer',
        'sales' => 'integer',
        'comment_count' => 'integer',
        'code' => 'integer',
        'shop_id' => 'integer',
        'category_id' => 'integer',
        'has_sku' => 'boolean',
        'state' => 'boolean',
    ];

    /**
     * 获取竞品监控任务
     */
    public function competitorMonitoringTask(): BelongsTo
    {
        return $this->belongsTo(CompetitorMonitoringTask::class);
    }

    /**
     * 获取关联的SKU历史数据
     */
    public function competitorProductSkusHistory(): HasMany
    {
        return $this->hasMany(CompetitorProductSkuHistory::class);
    }
}
