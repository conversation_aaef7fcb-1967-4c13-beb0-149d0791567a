<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CompetitorProductData extends Model
{
    use HasFactory;

    protected $fillable = [
        'competitor_monitoring_task_id',
        'item_id',
        'standardized_data',
        'last_collected_at',
        'code',
        'title',
        'product_image',
        'price',
        'lowest_price',
        'highest_price',
        'min_hand_price',
        'max_hand_price',
        'stock',
        'sales',
        'comment_count',
        'state',
        'has_sku',
        'item_type',
        'category_id',
        'category_path',
        'shop_id',
        'shop_name',
        'props',
        'promotion',
        'delivery_location',
        'product_url',
        'our_guide_price',
        'collection_batch_id',
    ];

    protected $casts = [
        'standardized_data' => 'array',
        'props' => 'json',
        'promotion' => 'json',
        'last_collected_at' => 'datetime',
        'price' => 'decimal:2',
        'lowest_price' => 'decimal:2',
        'highest_price' => 'decimal:2',
        'min_hand_price' => 'decimal:2',
        'max_hand_price' => 'decimal:2',
        'our_guide_price' => 'decimal:2',
        'stock' => 'integer',
        'sales' => 'integer',
        'comment_count' => 'integer',
        'code' => 'integer',
        'shop_id' => 'integer',
        'category_id' => 'integer',
        'has_sku' => 'boolean',
        'state' => 'boolean',
    ];

    /**
     * 获取竞品监控任务
     */
    public function competitorMonitoringTask(): BelongsTo
    {
        return $this->belongsTo(CompetitorMonitoringTask::class);
    }

    /**
     * 获取竞品商品SKU数据
     */
    public function competitorProductSkus(): HasMany
    {
        return $this->hasMany(CompetitorProductSku::class);
    }

    /**
     * 计算促销策略倾向
     * 解析"Promotion"字段中的促销类型，统计各促销策略的使用频率和占比
     */
    public function calculatePromotionStrategyTendency(): array
    {
        $promotions = $this->promotion ?? [];
        $strategies = [];
        
        foreach ($promotions as $promo) {
            $content = $promo['content'] ?? '';
            $subContent = $promo['sub_content'] ?? '';
            
            if (!empty($content)) {
                if (!isset($strategies[$content])) {
                    $strategies[$content] = [
                        'count' => 0,
                        'examples' => []
                    ];
                }
                $strategies[$content]['count']++;
                if (!empty($subContent) && !in_array($subContent, $strategies[$content]['examples'])) {
                    $strategies[$content]['examples'][] = $subContent;
                }
            }
        }
        
        // 计算占比
        $total = array_sum(array_column($strategies, 'count'));
        foreach ($strategies as $key => &$strategy) {
            $strategy['percentage'] = $total > 0 ? ($strategy['count'] / $total) * 100 : 0;
        }
        
        return $strategies;
    }

    /**
     * 计算单品价格综合折扣率（SKU级）
     * = (1 − (ΣsubPricei / ΣPricei)) * 100%
     */
    public function calculateComprehensiveDiscountRate(): float
    {
        $skus = $this->competitorProductSkus;
        
        if ($skus->isEmpty()) {
            return 0.0;
        }
        
        $totalSubPrice = $skus->sum('sub_price');
        $totalPrice = $skus->sum('price');
        
        if ($totalPrice <= 0) {
            return 0.0;
        }
        
        return (1 - ($totalSubPrice / $totalPrice)) * 100;
    }

    /**
     * 计算总体促销强度指数（SKU级）
     * = (促销SKU数 / 总SKU数) × (1 − 平均subPrice / 平均Price)
     */
    public function calculateOverallPromotionIntensity(): float
    {
        $skus = $this->competitorProductSkus;
        
        if ($skus->isEmpty()) {
            return 0.0;
        }
        
        $totalSkus = $skus->count();
        $promotionSkus = $skus->where('sub_price', '<', 'price')->count();
        
        $avgSubPrice = $skus->avg('sub_price');
        $avgPrice = $skus->avg('price');
        
        if ($avgPrice <= 0) {
            return 0.0;
        }
        
        $promotionRatio = $totalSkus > 0 ? $promotionSkus / $totalSkus : 0;
        $discountRatio = 1 - ($avgSubPrice / $avgPrice);
        
        return $promotionRatio * $discountRatio;
    }

    /**
     * 计算单品价格综合偏差率（SKU级）
     * = (我方指导价 - subPrice) / 我方指导价 × 100%
     */
    public function calculateComprehensiveDeviationRate(): float
    {
        if (!$this->our_guide_price || $this->our_guide_price <= 0) {
            return 0.0;
        }
        
        $avgSubPrice = $this->competitorProductSkus->avg('sub_price');
        
        if ($avgSubPrice === null) {
            return 0.0;
        }
        
        return (($this->our_guide_price - $avgSubPrice) / $this->our_guide_price) * 100;
    }

    /**
     * 计算单品最低价偏差率（SKU级）
     */
    public function calculateMinPriceDeviationRate(): float
    {
        $task = $this->competitorMonitoringTask;
        
        if (!$task || !$task->our_guide_price_min || $task->our_guide_price_min <= 0) {
            return 0.0;
        }
        
        $minSubPrice = $this->competitorProductSkus->min('sub_price');
        
        if ($minSubPrice === null) {
            return 0.0;
        }
        
        return (($task->our_guide_price_min - $minSubPrice) / $task->our_guide_price_min) * 100;
    }

    /**
     * 计算单品最高价偏差率（SKU级）
     */
    public function calculateMaxPriceDeviationRate(): float
    {
        $task = $this->competitorMonitoringTask;
        
        if (!$task || !$task->our_guide_price_max || $task->our_guide_price_max <= 0) {
            return 0.0;
        }
        
        $maxSubPrice = $this->competitorProductSkus->max('sub_price');
        
        if ($maxSubPrice === null) {
            return 0.0;
        }
        
        return (($task->our_guide_price_max - $maxSubPrice) / $task->our_guide_price_max) * 100;
    }

    /**
     * 获取价格历史数据用于趋势计算
     */
    public function getPriceHistory(int $days = 30): array
    {
        $history = CompetitorProductDataHistory::where('competitor_monitoring_task_id', $this->competitor_monitoring_task_id)
            ->where('item_id', $this->item_id)
            ->where('collected_at', '>=', now()->subDays($days))
            ->orderBy('collected_at')
            ->get(['collected_at', 'min_hand_price']);
        
        return $history->map(function ($item, $index) {
            return [
                'day' => $index + 1,
                'price' => $item->min_hand_price ?? 0
            ];
        })->toArray();
    }

    /**
     * 计算竞品价格趋势斜率
     * 使用线性回归计算斜率 a
     */
    public function calculatePriceTrendSlope(int $days = 30): float
    {
        $priceHistory = $this->getPriceHistory($days);
        
        if (count($priceHistory) < 2) {
            return 0.0;
        }
        
        $n = count($priceHistory);
        $sumDay = array_sum(array_column($priceHistory, 'day'));
        $sumPrice = array_sum(array_column($priceHistory, 'price'));
        $sumDayPrice = 0;
        $sumDaySquare = 0;
        
        foreach ($priceHistory as $data) {
            $sumDayPrice += $data['day'] * $data['price'];
            $sumDaySquare += $data['day'] * $data['day'];
        }
        
        $denominator = $n * $sumDaySquare - ($sumDay * $sumDay);
        
        if ($denominator == 0) {
            return 0.0;
        }
        
        $slope = ($n * $sumDayPrice - $sumDay * $sumPrice) / $denominator;
        
        return round($slope, 4);
    }
}
