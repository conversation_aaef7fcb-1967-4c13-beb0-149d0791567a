<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompetitorProductSkuHistory extends Model
{
    use HasFactory;

    protected $table = 'competitor_product_skus_history';

    protected $fillable = [
        'competitor_product_data_history_id',
        'sku_id',
        'name',
        'price',
        'sub_price',
        'sub_price_title',
        'quantity',
        'promotion',
        'our_guide_price',
        'promotion_strategy_score',
        'comprehensive_discount_rate',
        'overall_promotion_intensity',
        'comprehensive_deviation_rate',
        'min_price_deviation_rate',
        'max_price_deviation_rate',
        'price_trend_slope',
    ];

    protected $casts = [
        'promotion' => 'json',
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'our_guide_price' => 'decimal:2',
        'promotion_strategy_score' => 'decimal:4',
        'comprehensive_discount_rate' => 'decimal:4',
        'overall_promotion_intensity' => 'decimal:4',
        'comprehensive_deviation_rate' => 'decimal:4',
        'min_price_deviation_rate' => 'decimal:4',
        'max_price_deviation_rate' => 'decimal:4',
        'price_trend_slope' => 'decimal:4',
        'quantity' => 'integer',
    ];

    /**
     * 获取竞品商品数据历史
     */
    public function competitorProductDataHistory(): BelongsTo
    {
        return $this->belongsTo(CompetitorProductDataHistory::class);
    }
}
