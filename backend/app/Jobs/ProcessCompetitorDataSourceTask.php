<?php

namespace App\Jobs;

use App\Models\CompetitorMonitoringTask;
use App\Models\CompetitorProductData;
use App\Models\CompetitorProductSku;
use App\Models\CompetitorProductDataHistory;
use App\Models\CompetitorProductSkuHistory;
use App\Models\DataSource;
use App\Services\DataCollectionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessCompetitorDataSourceTask implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $dataSourceId;
    protected $targetProducts;
    protected $additionalParams;
    protected $competitorTaskId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $dataSourceId, array $targetProducts = [], array $additionalParams = [], int $competitorTaskId = null)
    {
        $this->dataSourceId = $dataSourceId;
        $this->targetProducts = $targetProducts;
        $this->additionalParams = $additionalParams;
        $this->competitorTaskId = $competitorTaskId;
    }

    /**
     * Execute the job.
     */
    public function handle(DataCollectionService $dataCollectionService): void
    {
        try {
            Log::info('开始处理竞品数据采集任务', [
                'data_source_id' => $this->dataSourceId,
                'competitor_task_id' => $this->competitorTaskId,
                'target_products_count' => count($this->targetProducts)
            ]);

            $dataSource = DataSource::find($this->dataSourceId);
            if (!$dataSource) {
                Log::error('数据源不存在', ['data_source_id' => $this->dataSourceId]);
                return;
            }

            $competitorTask = null;
            if ($this->competitorTaskId) {
                $competitorTask = CompetitorMonitoringTask::find($this->competitorTaskId);
                if (!$competitorTask) {
                    Log::error('竞品监控任务不存在', ['competitor_task_id' => $this->competitorTaskId]);
                    return;
                }
            }

            // 生成采集批次ID
            $collectionBatchId = Str::uuid()->toString();
            $collectedAt = now();

            // 调用数据采集服务
            $collectedData = $dataCollectionService->collectCompetitorData(
                $dataSource,
                $this->targetProducts,
                $this->additionalParams
            );

            if (empty($collectedData)) {
                Log::warning('未采集到竞品数据', [
                    'data_source_id' => $this->dataSourceId,
                    'competitor_task_id' => $this->competitorTaskId
                ]);
                
                if ($competitorTask) {
                    $competitorTask->recordExecution(false, '未采集到数据');
                }
                return;
            }

            $processedCount = 0;
            $errorCount = 0;

            foreach ($collectedData as $productData) {
                try {
                    $this->processCompetitorProductData(
                        $productData,
                        $competitorTask,
                        $collectionBatchId,
                        $collectedAt
                    );
                    $processedCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error('处理竞品商品数据失败', [
                        'item_id' => $productData['item_id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            Log::info('竞品数据采集任务完成', [
                'data_source_id' => $this->dataSourceId,
                'competitor_task_id' => $this->competitorTaskId,
                'processed_count' => $processedCount,
                'error_count' => $errorCount,
                'collection_batch_id' => $collectionBatchId
            ]);

            // 更新任务执行记录
            if ($competitorTask) {
                $competitorTask->recordExecution(
                    $errorCount === 0,
                    $errorCount > 0 ? "处理了 {$processedCount} 个商品，{$errorCount} 个失败" : "成功处理 {$processedCount} 个商品"
                );
                
                // 更新任务统计信息
                $competitorTask->updateStatistics();
            }

        } catch (\Exception $e) {
            Log::error('竞品数据采集任务执行失败', [
                'data_source_id' => $this->dataSourceId,
                'competitor_task_id' => $this->competitorTaskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($competitorTask) {
                $competitorTask->recordExecution(false, $e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * 处理单个竞品商品数据
     */
    protected function processCompetitorProductData(
        array $productData,
        ?CompetitorMonitoringTask $competitorTask,
        string $collectionBatchId,
        \Carbon\Carbon $collectedAt
    ): void {
        $itemId = $productData['item_id'];
        $taskId = $competitorTask ? $competitorTask->id : null;

        // 准备商品数据
        $productAttributes = [
            'competitor_monitoring_task_id' => $taskId,
            'item_id' => $itemId,
            'standardized_data' => $productData,
            'last_collected_at' => $collectedAt,
            'code' => $productData['code'] ?? null,
            'title' => $productData['title'] ?? null,
            'product_image' => $productData['product_image'] ?? null,
            'price' => $productData['price'] ?? null,
            'lowest_price' => $productData['lowest_price'] ?? null,
            'highest_price' => $productData['highest_price'] ?? null,
            'min_hand_price' => $productData['min_hand_price'] ?? null,
            'max_hand_price' => $productData['max_hand_price'] ?? null,
            'stock' => $productData['stock'] ?? null,
            'sales' => $productData['sales'] ?? null,
            'comment_count' => $productData['comment_count'] ?? null,
            'state' => $productData['state'] ?? true,
            'has_sku' => $productData['has_sku'] ?? false,
            'item_type' => $productData['item_type'] ?? null,
            'category_id' => $productData['category_id'] ?? null,
            'category_path' => $productData['category_path'] ?? null,
            'shop_id' => $productData['shop_id'] ?? null,
            'shop_name' => $productData['shop_name'] ?? null,
            'props' => $productData['props'] ?? null,
            'promotion' => $productData['promotion'] ?? null,
            'delivery_location' => $productData['delivery_location'] ?? null,
            'product_url' => $productData['product_url'] ?? null,
            'our_guide_price' => $competitorTask ? $competitorTask->our_guide_price_avg : null,
            'collection_batch_id' => $collectionBatchId,
        ];

        // 更新或创建商品数据
        $competitorProduct = null;
        if ($taskId) {
            $competitorProduct = CompetitorProductData::updateOrCreate(
                [
                    'competitor_monitoring_task_id' => $taskId,
                    'item_id' => $itemId
                ],
                $productAttributes
            );
        }

        // 创建历史记录
        $historyData = array_merge($productAttributes, [
            'collected_at' => $collectedAt
        ]);
        $productHistory = CompetitorProductDataHistory::create($historyData);

        // 处理SKU数据
        if (!empty($productData['skus']) && is_array($productData['skus'])) {
            foreach ($productData['skus'] as $skuData) {
                $this->processCompetitorSkuData(
                    $skuData,
                    $competitorProduct,
                    $productHistory,
                    $competitorTask
                );
            }
        }

        // 计算并更新核心指标
        if ($competitorProduct) {
            $competitorProduct->updateAllMetrics();
        }
    }

    /**
     * 处理SKU数据
     */
    protected function processCompetitorSkuData(
        array $skuData,
        ?CompetitorProductData $competitorProduct,
        CompetitorProductDataHistory $productHistory,
        ?CompetitorMonitoringTask $competitorTask
    ): void {
        $skuId = $skuData['sku_id'] ?? null;
        if (!$skuId) {
            return;
        }

        $skuAttributes = [
            'sku_id' => $skuId,
            'name' => $skuData['name'] ?? null,
            'price' => $skuData['price'] ?? null,
            'sub_price' => $skuData['sub_price'] ?? null,
            'sub_price_title' => $skuData['sub_price_title'] ?? null,
            'quantity' => $skuData['quantity'] ?? null,
            'promotion' => $skuData['promotion'] ?? null,
            'our_guide_price' => $competitorTask ? $competitorTask->our_guide_price_avg : null,
        ];

        // 更新或创建SKU数据
        if ($competitorProduct) {
            $competitorSku = CompetitorProductSku::updateOrCreate(
                [
                    'competitor_product_data_id' => $competitorProduct->id,
                    'sku_id' => $skuId
                ],
                $skuAttributes
            );

            // 计算并更新SKU级别的核心指标
            $competitorSku->updateAllMetrics();
        }

        // 创建SKU历史记录
        $skuHistoryAttributes = array_merge($skuAttributes, [
            'competitor_product_data_history_id' => $productHistory->id
        ]);
        CompetitorProductSkuHistory::create($skuHistoryAttributes);
    }
}
