<?php

namespace App\Services;

use App\Jobs\ProcessAlertChecking;
use App\Jobs\ProcessTaskGroupCollectionComplete;
use App\Models\DataSource;
use App\Models\ProductData;
use App\Models\ProductSku;
use App\Models\ProductDataHistory;
use App\Models\ProductSkuHistory;
use App\Models\CompetitorProductData;
use App\Models\CompetitorProductSku;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DataCollectionService
{
    /**
     * 当前处理的监控任务ID
     */
    private ?int $monitoringTaskId = null;

    /**
     * 当前采集批次ID
     */
    private ?string $collectionBatchId = null;

    /**
     * 执行数据采集和标准化
     *
     * @param int $dataSourceId 数据源ID
     * @param mixed $itemId 目标商品ID
     * @param array $extraParams 额外参数
     * @param int|null $monitoringTaskId 监控任务ID（如果提供，将用于数据存储）
     * @param string|null $collectionBatchId 采集批次ID（用于任务分组预警汇总）
     * @return array
     * @throws \Exception
     */
    public function collectAndStandardize(int $dataSourceId, $itemId, array $extraParams = [], ?int $monitoringTaskId = null, ?string $collectionBatchId = null): array
    {
        // 设置当前处理的监控任务ID和采集批次ID
        $this->monitoringTaskId = $monitoringTaskId;
        $this->collectionBatchId = $collectionBatchId;

        $processStartTime = now();
        $productId = is_array($itemId) && isset($itemId['product_id']) ? $itemId['product_id'] : $itemId;

        // 清理商品ID中的特殊字符
        Log::info('DEBUG: 开始清理商品ID', [
            'original_productId' => $productId,
            'is_string' => is_string($productId)
        ]);

        if (is_string($productId)) {
            $originalProductId = $productId;
            $productId = preg_replace('/[^\w\-]/', '', $productId); // 只保留字母、数字、下划线和连字符
            Log::info('DEBUG: 商品ID清理完成', [
                'original' => $originalProductId,
                'cleaned' => $productId,
                'changed' => $originalProductId !== $productId
            ]);
            if ($originalProductId !== $productId) {
                Log::info('商品ID已清理', [
                    'original' => $originalProductId,
                    'cleaned' => $productId
                ]);
            }
        }

        try {
            Log::info('开始数据采集与标准化', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'extra_params' => $extraParams,
                'monitoring_task_id' => $monitoringTaskId,
                'process_start_time' => $processStartTime->toDateTimeString()
            ]);

            // 1. 获取数据源配置
            Log::info('步骤1：开始获取数据源配置', ['data_source_id' => $dataSourceId]);
            $dataSource = DataSource::findOrFail($dataSourceId);
            Log::info('步骤1：数据源配置加载完成', [
                'data_source_id' => $dataSource->id,
                'data_source_name' => $dataSource->name,
                'api_method' => $dataSource->api_method,
                'api_url' => $dataSource->config['url'] ?? 'not_set',
                'timeout' => $dataSource->timeout,
                'config_full' => $dataSource->config,
                'field_mapping' => $dataSource->field_mapping,
                'default_params' => $dataSource->default_params
            ]);

            // 2. 构造API请求
            Log::info('步骤2：开始构造API请求', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'cleaned_product_id' => $productId,
                'extra_params' => $extraParams
            ]);
            $requestData = $this->buildApiRequest($dataSource, $productId, $extraParams);
            Log::info('步骤2：API请求构造完成', [
                'request_url' => $requestData['url'],
                'request_method' => $requestData['method'],
                'request_params' => $requestData['params'],
                'request_headers' => $requestData['headers'] ?? 'None',
                'request_timeout' => $requestData['timeout']
            ]);

            // 3. 发起HTTP请求
            Log::info('步骤3：开始发起HTTP请求');
            $response = $this->makeHttpRequest($requestData);
            Log::info('步骤3：HTTP请求完成', [
                'response_status' => $response->status(),
                'response_headers' => $response->headers(),
                'response_size' => strlen($response->body()) . ' bytes',
                'response_successful' => $response->successful()
            ]);

            // 4. 解析响应
            Log::info('步骤4：开始解析API响应');
            $rawData = $this->parseResponse($response, $dataSource);
            Log::info('步骤4：API响应解析完成', [
                'raw_data_keys' => array_keys($rawData),
                'raw_data_sample' => array_slice($rawData, 0, 3, true), // 只记录前3个字段作为样本
                'raw_data_full' => $rawData // 完整的原始数据
            ]);

            // 5. 标准化数据
            Log::info('步骤5：开始数据标准化', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'raw_data_to_standardize' => $rawData
            ]);
            $standardizedData = $this->standardizeData($rawData, $dataSource->field_mapping);
            Log::info('步骤5：数据标准化完成', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'standardized_product_fields_count' => count($standardizedData['product_fields']),
                'standardized_sku_count' => count($standardizedData['sku_fields']),
            ]);

            // 6. 存储数据
            Log::info('步骤6：开始存储数据到数据库');
            $productData = $this->storeData($dataSource, $itemId, $rawData, $standardizedData, $monitoringTaskId);
            Log::info('步骤6：数据存储完成', [
                'product_data_id' => $productData->id,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id,
                'operation_type' => $productData->wasRecentlyCreated ? 'CREATE' : 'UPDATE',
                'created_at' => $productData->created_at,
                'updated_at' => $productData->updated_at,
                'last_collected_at' => $productData->last_collected_at,
                'raw_data' => $rawData,
                'standardized_data' => [
                    'product_fields' => $standardizedData['product_fields'],
                    'sku_fields' => $standardizedData['sku_fields'],
                ]
            ]);

            // 7. 触发警报检查
            Log::info('步骤7：开始触发警报检查');
            $this->dispatchAlertChecking($productData);
            Log::info('步骤7：警报检查任务已分发');

            // 8. 如果有采集批次ID，触发任务分组完成检查
            if ($this->collectionBatchId && $this->monitoringTaskId) {
                Log::info('步骤8：触发任务分组完成检查');
                $this->dispatchTaskGroupCollectionComplete();
                Log::info('步骤8：任务分组完成检查任务已分发');
            }

            Log::info('数据采集与标准化流程全部完成', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'product_data_id' => $productData->id,
                'process_end_time' => now()->toDateTimeString(),
                'final_result' => [
                    'raw_data' => $rawData,
                    'standardized_data' => $standardizedData,
                    'database_record_id' => $productData->id
                ]
            ]);

            return [
                'success' => true,
                'product_data_id' => $productData->id,
                'raw_data' => $rawData,
                'standardized_data' => $standardizedData,
            ];

        } catch (\Exception $e) {
            Log::error('数据采集与标准化流程失败', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'process_end_time' => now()->toDateTimeString()
            ]);
            
            $this->handleException($e, $dataSourceId, $itemId);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 构造API请求
     */
    private function buildApiRequest(DataSource $dataSource, $itemId, array $extraParams): array
    {
        $config = $dataSource->config ?? [];
        Log::info('开始构造API请求', [
            'data_source_id' => $dataSource->id,
            'item_id' => $itemId,
            'config_keys' => array_keys($config),
            'auth_type' => $config['auth_type'] ?? 'none',
        ]);

        $url = $config['url'] ?? '';

        // 从$itemId中提取真实的商品ID，兼容新旧数据格式
        $productId = is_array($itemId) && isset($itemId['product_id']) ? $itemId['product_id'] : $itemId;

        // 动态替换URL中的占位符
        if (is_string($productId) || is_numeric($productId)) {
            $url = str_replace('{item_id}', $productId, $url);
        }

        // 合并基础参数 (来自config的默认参数、来自任务的额外参数)
        $params = array_merge($config['params'] ?? [], $dataSource->default_params ?? [], $extraParams);
        
        // 确保商品ID作为参数传递
        // 参数名可以在数据源配置中指定，默认为'item_id'
        $itemIdParamName = $config['item_id_param'] ?? 'item_id';
        if (!isset($params[$itemIdParamName]) && !empty($productId)) {
            $params[$itemIdParamName] = $productId;
        }

        // 处理动态参数替换，例如从itemId中获取其他值
        foreach ($params as $key => $value) {
            if (is_string($value) && str_starts_with($value, '{') && str_ends_with($value, '}')) {
                $placeholder = trim($value, '{}');
                if (is_array($itemId) && isset($itemId[$placeholder])) {
                    $replacement = $itemId[$placeholder];
                    // 确保替换值是字符串
                    if (is_scalar($replacement)) {
                        $params[$key] = (string) $replacement;
                    } else {
                        // 如果是数组或对象，可以选择json编码或移除
                        unset($params[$key]);
                        Log::warning('动态参数替换失败：占位符 ' . $placeholder . ' 的值不是标量，已移除。', [
                            'key' => $key,
                            'placeholder' => $placeholder,
                            'value_type' => gettype($replacement)
                        ]);
                    }
                }
            }
        }
        
        // 处理认证配置
        $headers = $config['headers'] ?? [];
        $authType = $config['auth_type'] ?? 'none';
        
        Log::info('处理认证配置', [
            'auth_type' => $authType,
            'api_key_name' => $config['api_key_name'] ?? null,
            'has_api_key_value' => !empty($config['api_key_value']),
            'has_token' => !empty($config['token']),
        ]);

        switch ($authType) {
            case 'bearer':
                if (!empty($config['token'])) {
                    $headers['Authorization'] = 'Bearer ' . $config['token'];
                    Log::info('添加Bearer认证头');
                }
                break;
                
            case 'header_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $headers[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到请求头', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'query_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $params[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到查询参数', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'none':
            default:
                // 兼容旧版本：如果没有设置认证方式但config.params中有token，则使用它
                if (!empty($config['params']['token'])) {
                    Log::info('检测到旧版本token配置，使用config.params中的token');
                    // token已经在上面合并参数时包含了，这里只记录日志
                }
                
                // 同时检查config中的token并添加到查询参数
                if (!empty($config['token'])) {
                    $params['token'] = $config['token'];
                    Log::info('添加config中的token到查询参数', [
                        'token' => $config['token']
                    ]);
                }
                break;
        }
        
        // 构造请求数据
        $requestData = [
            'method' => strtoupper($config['method'] ?? 'GET'),
            'url' => $url,
            'params' => $params,
            'timeout' => $config['timeout'] ?? $dataSource->timeout ?? 30,
        ];
        
        // 只有当headers不为空时才添加
        if (!empty($headers)) {
            $requestData['headers'] = $headers;
        }
        
        Log::info('API请求构造完成', [
            'final_url' => $url,
            'final_params' => $params,
            'final_headers' => $headers,
            'auth_applied' => $authType !== 'none'
        ]);
        
        return $requestData;
    }

    /**
     * 发起HTTP请求
     */
    private function makeHttpRequest(array $requestData): \Illuminate\Http\Client\Response
    {
        // 构造完整的请求URL用于日志记录
        $fullUrl = $requestData['url'] . '?' . http_build_query($requestData['params']);
        Log::info('发起API请求', [
            'url' => $fullUrl,
            'method' => $requestData['method'],
            'headers' => $requestData['headers'] ?? 'None',
            'params' => $requestData['params'],
            'timeout' => $requestData['timeout'],
        ]);

        $response = Http::timeout($requestData['timeout']);
        
        // 只有当headers存在且不为空时才设置
        if (isset($requestData['headers']) && !empty($requestData['headers'])) {
            $response = $response->withHeaders($requestData['headers']);
        }
        
        // 解决SSL证书问题
        // 在生产环境中，应该配置php.ini指向正确的cacert.pem文件
        // 这里为了开发方便，暂时禁用SSL验证
        if (app()->environment('local')) {
            $response->withoutVerifying();
        }

        switch ($requestData['method']) {
            case 'POST':
                return $response->post($requestData['url'], $requestData['params']);
            case 'PUT':
                return $response->put($requestData['url'], $requestData['params']);
            case 'PATCH':
                return $response->patch($requestData['url'], $requestData['params']);
            case 'DELETE':
                return $response->delete($requestData['url'], $requestData['params']);
            default: // GET
                return $response->get($requestData['url'], $requestData['params']);
        }
    }

    /**
     * 解析HTTP响应
     */
    private function parseResponse(\Illuminate\Http\Client\Response $response, DataSource $dataSource): array
    {
        Log::info('收到API响应', [
            'status_code' => $response->status(),
            'headers' => $response->headers(),
            'body_preview' => mb_substr($response->body(), 0, 1000) . (mb_strlen($response->body()) > 1000 ? '...' : ''), // 记录前1000个字符
        ]);

        // 记录完整的响应体
        Log::info('API完整响应体', [
            'response_body' => $response->body(),
            'response_length' => strlen($response->body())
        ]);

        if (!$response->successful()) {
            throw new \Exception('API请求失败: HTTP ' . $response->status());
        }

        $responseBody = $response->body();
        Log::info('API响应体获取成功', [
            'response_length' => strlen($responseBody),
            'response_preview' => Str::limit($responseBody, 1000)
        ]);

        $data = $response->json();

        if (is_null($data)) {
            Log::error('JSON解析失败', [
                'response_body' => $responseBody,
                'json_last_error' => json_last_error_msg()
            ]);
            throw new \Exception("无法解析JSON响应: " . $responseBody);
        }

        Log::info('JSON解析成功', [
            'data_type' => gettype($data),
            'data_structure' => is_array($data) ? array_keys($data) : 'not_array',
            'data_count' => is_array($data) ? count($data) : 'not_countable',
            'parsed_data' => $data // 记录完整的解析后数据
        ]);

        // 检查API返回的code字段，如果是400表示商品下架
        if (isset($data['code']) && $data['code'] == 400) {
            Log::info('检测到商品下架状态', [
                'api_code' => $data['code'],
                'api_data' => $data['data'] ?? null,
                'left_nums' => $data['left_nums'] ?? null
            ]);
            
            // 返回一个特殊的下架状态数据结构
            return [
                'code' => 400,
                'state' => 0, // 下架状态
                'data' => $data['data'] ?? [],
                'left_nums' => $data['left_nums'] ?? null,
                'is_offline' => true // 标记为下架商品
            ];
        }

        return $data;
    }

    /**
     * 根据字段映射标准化数据
     *
     * @param array $rawData
     * @param array $mappingRules
     * @return array
     */
    private function standardizeData(array $rawData, ?array $mappingRules): array
    {
        if (empty($mappingRules) || empty($mappingRules['fields'])) {
            return ['product_fields' => [], 'sku_fields' => []];
        }

        $productFields = [];
        $skuFieldsList = [];
        $mappings = $mappingRules['fields'];
        
        // 定义应从原始数据根部提取的元数据字段
        $metadataFields = ['code'];

        // 检查是否为下架商品
        if (isset($rawData['is_offline']) && $rawData['is_offline'] === true) {
            Log::info('处理下架商品数据标准化', [
                'raw_data' => $rawData
            ]);
            
            // 对于下架商品，只设置基本的状态信息
            return [
                'product_fields' => [
                    'state' => 0, // 下架状态
                    'code' => $rawData['code'] ?? 400,
                ],
                'sku_fields' => []
            ];
        }

        // 首先，从原始数据中提取最外层的数据节点
        $dataNode = Arr::get($rawData, $mappingRules['data_path'] ?? null, $rawData);
        if (!is_array($dataNode)) {
            Log::warning('Data node for standardization is not an array or does not exist.', [
                'data_path' => $mappingRules['data_path'] ?? 'not_set',
                'raw_data' => $rawData,
            ]);
            $dataNode = []; // 避免后续错误
        }

        foreach ($mappings as $targetField => $source) {
            // 如果 source 是一个数组，它可能是skus集合的定义，直接跳过，因为它会被单独处理
            if (is_array($source)) {
                continue;
            }

            // 兼容数组下标写法：如 pic_urls[0] => pic_urls.0
            if (is_string($source) && preg_match('/(.+)\\[(\\d+)\\]$/', $source, $matches)) {
                $source = $matches[1] . '.' . $matches[2];
            }

            // 根据字段类型决定数据源：元数据从顶层取，其他从data_path节点取
            $dataSourceNode = in_array($targetField, $metadataFields) ? $rawData : $dataNode;

            // 提取数据，并增加驼峰式兼容
            $value = $this->extractValue($dataSourceNode, $source);
            if ($value === null) {
                $camelCaseSource = Str::camel($source);
                if ($camelCaseSource !== $source) {
                    $value = $this->extractValue($dataSourceNode, $camelCaseSource);
                }
            }

            // 处理简单的顶层产品字段
            if ($value !== null) {
                $transformation = $mappingRules['transformations'][$targetField] ?? null;
                $productFields[$targetField] = $this->transformValue($value, $transformation, $targetField);
            }
        }
        
        // 专门处理SKU集合
        if (isset($mappings['skus']) && is_array($mappings['skus'])) {
            $skuConfig = $mappings['skus'];
            $collectionSourcePath = $skuConfig['path'] ?? null;
            $collectionData = $collectionSourcePath ? Arr::get($dataNode, $collectionSourcePath) : [];
            $skuMappings = $skuConfig['fields'];

            if (is_array($collectionData)) {
                foreach ($collectionData as $item) {
                    $singleSkuFields = [];
                    foreach ($skuMappings as $skuTargetField => $skuSourcePath) {
                        $value = Arr::get($item, $skuSourcePath);
                        if ($value !== null) {
                             $transformation = $mappingRules['transformations']['skus'][$skuTargetField] ?? null;
                             $singleSkuFields[$skuTargetField] = $this->transformValue($value, $transformation, $skuTargetField);
                        }
                    }
                    if (!empty($singleSkuFields)) {
                        $skuFieldsList[] = $singleSkuFields;
                    }
                }
            }
        }
        
        // 采集商品状态逻辑调整 (基于已经提取的code)
        $code = $productFields['code'] ?? null;
        if ($code == 200) {
            $stateText = Arr::get($dataNode, 'state', null);
            if ($stateText === '上架') {
                $productFields['state'] = 1;
            } elseif ($stateText === '下架') {
                $productFields['state'] = 0;
            } // 其它情况不赋值，保持原有逻辑
        }
        
        // 计算最低和最高到手价
        if (!empty($skuFieldsList)) {
            $handPrices = [];
            foreach ($skuFieldsList as $sku) {
                $promotionPrice = $sku['promotion_price'] ?? null;
                if ($promotionPrice !== null && is_numeric($promotionPrice) && $promotionPrice > 0) {
                    $handPrices[] = (float)$promotionPrice;
                }
            }

            if (!empty($handPrices)) {
                $productFields['min_hand_price'] = min($handPrices);
                $productFields['max_hand_price'] = max($handPrices);
                Log::info('计算到手价完成', [
                    'hand_prices' => $handPrices,
                    'min_hand_price' => $productFields['min_hand_price'],
                    'max_hand_price' => $productFields['max_hand_price']
                ]);
            }
        }

        return [
            'product_fields' => $productFields,
            'sku_fields' => $skuFieldsList,
        ];
    }

    /**
     * Extracts a value from raw data using a mapping rule, with transformation logic.
     * This is a simplified version for the refactoring.
     * A more complete implementation would be in the original `transformValue` method.
     */
    private function extractValue(array $rawData, $mapping)
    {
        if (is_string($mapping)) {
            return Arr::get($rawData, $mapping);
        }
        // Placeholder for more complex mapping with transformations
        return Arr::get($rawData, $mapping['source']);
    }

    /**
     * 转换单个值
     */
    private function transformValue($value, ?array $transformations, string $fieldType)
    {
        if (empty($transformations)) {
            return $value;
        }

        // 兼容旧的格式
        $type = $transformations['type'] ?? null;
        
        // 类型转换
        switch ($type) {
            case 'integer':
                // 先检查是否有映射规则
                if (isset($transformations['mapping']) && is_array($transformations['mapping'])) {
                    $key = (string)$value;
                    $value = $transformations['mapping'][$key] ?? (int) $value;
                } else {
                    $value = (int) $value;
                }
                break;
            case 'float':
                $value = (float) $value;
                break;
            case 'boolean':
                if (isset($transformations['mapping']) && is_array($transformations['mapping'])) {
                    $key = strtolower((string)$value);
                    $value = $transformations['mapping'][$key] ?? (filter_var($value, FILTER_VALIDATE_BOOLEAN));
                } else {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                }
                break;
            case 'string':
                $value = (string) $value;
                break;
            case 'array':
                if (isset($transformations['ensure_array']) && $transformations['ensure_array'] && !is_array($value)) {
                    $value = $value === null ? [] : [$value];
                }
                break;
        }

        // 自定义转换规则
        if (isset($transformations['rules']) && is_array($transformations['rules'])) {
            foreach ($transformations['rules'] as $rule => $options) {
                switch ($rule) {
                    case 'min_from_array': // 例如: '最低到手价'
                        if (is_array($value)) {
                            $numericValues = array_filter($value, 'is_numeric');
                            $value = !empty($numericValues) ? min($numericValues) : null;
                        }
                        break;
                    case 'regex_match':
                        if (is_string($value) && isset($options['pattern'])) {
                            preg_match($options['pattern'], $value, $matches);
                            $value = $matches[$options['index'] ?? 0] ?? null;
                        }
                        break;
                    case 'string_replace':
                        if (is_string($value) && isset($options['search'], $options['replace'])) {
                            $value = str_replace($options['search'], $options['replace'], $value);
                        }
                        break;
                    case 'timestamp_to_date':
                        if (is_numeric($value)) {
                        $value = \Carbon\Carbon::createFromTimestamp($value)->format($options['format'] ?? 'Y-m-d H:i:s');
                        }
                        break;
                    case 'default':
                        if (empty($value) && isset($options['value'])) {
                            $value = $options['value'];
                        }
                        break;
                }
            }
        }
        
        // 兼容旧的默认值设置
        if (isset($transformations['default']) && empty($value)) {
             $value = $transformations['default'];
        }

        return $value;
    }

    /**
     * 存储数据到数据库
     *
     * @param DataSource $dataSource
     * @param mixed $itemId
     * @param array $rawData
     * @param array $standardizedData
     * @param int|null $monitoringTaskId
     * @return ProductData
     */
    private function storeData(DataSource $dataSource, $itemId, array $rawData, array $standardizedData, ?int $monitoringTaskId): ProductData
    {
        $productFields = $standardizedData['product_fields'] ?? [];
        $skuFields = $standardizedData['sku_fields'] ?? [];
        $promotions = $standardizedData['promotions'] ?? [];
        $productId = is_array($itemId) && isset($itemId['product_id']) ? $itemId['product_id'] : $itemId;

        Log::info('准备存储数据', [
            'promotions_from_standardized' => $promotions,
            'product_fields_keys' => array_keys($productFields),
            'sku_fields_count' => count($skuFields)
        ]);

        // Helper function to safely convert numeric fields
        $safeNumeric = function ($value, $default = 0) {
            return is_numeric($value) ? $value : $default;
        };

        // Helper function to safely convert JSON fields
        $safeJson = function ($value) {
            if (is_null($value)) {
                return null;
            }
            if (is_array($value)) {
                return $value; // Laravel will auto-encode to JSON
            }
            if (is_string($value)) {
                $decoded = json_decode($value, true);
                return $decoded !== null ? $decoded : $value;
            }
            return $value;
        };

        // Prepare data for ProductData model
        $updateData = [
            'monitoring_task_id' => $monitoringTaskId,
            'item_id' => $productId,
            'collection_batch_id' => $this->collectionBatchId,
            'raw_data' => json_encode($rawData),
            'standardized_data' => json_encode($standardizedData),
            'last_collected_at' => now(),
            'code' => $safeNumeric($productFields['code'] ?? null), // 添加code字段，确保数字类型
            'title' => $productFields['title'] ?? null,
            'product_image' => $productFields['main_image_url'] ?? $productFields['product_image'] ?? null,
            'sales' => $safeNumeric($productFields['sales'] ?? null),
            'comment_count' => $safeNumeric($productFields['comment_count'] ?? null), // 修复空字符串问题
            'state' => $productFields['state'] ?? null, // 移除默认值，严格依赖API数据
            'has_sku' => !empty($skuFields),
            'item_type' => $productFields['item_type'] ?? null,
            'category_id' => $productFields['category_id'] ?? null,
            'category_path' => $productFields['category_path'] ?? null,
            'shop_id' => $safeNumeric($productFields['shop_id'] ?? null),
            'shop_name' => $productFields['shop_name'] ?? null,
            'props' => $safeJson($productFields['props'] ?? null),
            'promotion' => $safeJson($promotions), // 修复：使用safeJson确保正确的数据类型
            'delivery_location' => $productFields['delivery_location'] ?? null,
            // 价格字段：优先使用subPrice作为主要价格，如果subPrice为0或空则使用price
            'price' => $safeNumeric($this->getEffectivePrice($productFields)),
            'lowest_price' => $safeNumeric($productFields['lowest_price'] ?? null) ?? $safeNumeric($productFields['price'] ?? null),
            'highest_price' => $safeNumeric($productFields['highest_price'] ?? null) ?? $safeNumeric($productFields['price'] ?? null),
            'min_hand_price' => $safeNumeric($productFields['min_hand_price'] ?? null),
            'max_hand_price' => $safeNumeric($productFields['max_hand_price'] ?? null),
        ];

        Log::info('准备更新的产品数据', [
            'promotions_in_update_data' => $updateData['promotion'],
            'update_data_keys' => array_keys($updateData)
        ]);

        // 确保monitoring_task_id不为null，如果为null则使用默认值或跳过
        if ($monitoringTaskId === null) {
            Log::warning('监控任务ID为null，无法创建或更新产品数据', [
                'item_id' => $productId,
                'monitoring_task_id' => $monitoringTaskId
            ]);
            throw new \Exception('监控任务ID不能为空');
        }

        Log::info('准备执行updateOrCreate', [
            'item_id' => $productId,
            'monitoring_task_id' => $monitoringTaskId,
            'update_data_monitoring_task_id' => $updateData['monitoring_task_id']
        ]);

        $productData = ProductData::updateOrCreate(
            [
                'item_id' => $productId,
                'monitoring_task_id' => $monitoringTaskId,
            ],
            $updateData
        );

        Log::info('产品数据更新/创建成功', ['product_data_id' => $productData->id]);

        // Sync SKUs
        if ($productData->id) {
            // Delete old SKUs first
            $productData->skus()->delete();

            if (!empty($skuFields)) {
                // 获取监控任务中的官方指导价配置
                $officialGuidePrice = $this->getOfficialGuidePriceForProduct($monitoringTaskId, $productId);

                $skuDataToInsert = array_map(function ($sku) use ($productData, $safeNumeric, $officialGuidePrice) {
                    return [
                        'product_data_id' => $productData->id,
                        'sku_id' => $sku['sku_id'] ?? null,
                        'name' => $sku['name'] ?? $sku['sku_name'] ?? 'SKU-' . ($sku['sku_id'] ?? 'unknown'),
                        'price' => $safeNumeric($sku['price'] ?? 0),
                        'sub_price' => $safeNumeric($sku['sub_price'] ?? $sku['promotion_price'] ?? null),
                        'sub_price_title' => $sku['sub_price_title'] ?? null,
                        'quantity' => $safeNumeric($sku['stock'] ?? 0),
                        'image_url' => $sku['image_url'] ?? null,
                        'official_guide_price' => $safeNumeric($officialGuidePrice),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }, $skuFields);
                ProductSku::insert($skuDataToInsert);
            }
        }

        // 更新产品总库存
        $totalStock = $productData->skus()->sum('quantity');
        $productData->stock = $totalStock;
        $productData->save();

        // 保存到历史表
        $this->saveToHistory($productData, $skuFields, $safeNumeric, $monitoringTaskId, $productId);
        
        return $productData;
    }

    /**
     * 获取商品的官方指导价
     *
     * @param int|null $monitoringTaskId
     * @param string $itemId
     * @return float|null
     */
    private function getOfficialGuidePriceForProduct(?int $monitoringTaskId, string $itemId): ?float
    {
        if (!$monitoringTaskId) {
            return null;
        }

        $monitoringTask = \App\Models\MonitoringTask::find($monitoringTaskId);
        if (!$monitoringTask) {
            return null;
        }

        $targetProducts = $monitoringTask->target_products ?? [];

        // 如果target_products是新格式（包含official_guide_price的对象数组）
        if (!empty($targetProducts) && is_array($targetProducts)) {
            foreach ($targetProducts as $product) {
                if (is_array($product) &&
                    isset($product['product_id']) &&
                    $product['product_id'] == $itemId &&
                    isset($product['official_guide_price'])) {
                    return (float)$product['official_guide_price'];
                }
            }
        }

        return null;
    }

    /**
     * 保存数据到历史表
     *
     * @param ProductData $productData
     * @param array $skuFields
     * @param callable $safeNumeric
     * @param int|null $monitoringTaskId
     * @param string $productId
     * @return void
     */
    private function saveToHistory(ProductData $productData, array $skuFields, callable $safeNumeric, ?int $monitoringTaskId = null, string $productId = null): void
    {
        try {
            Log::info('开始保存历史数据', [
                'product_data_id' => $productData->id,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id
            ]);

            // 保存商品历史数据
            $historyData = [
                'monitoring_task_id' => $productData->monitoring_task_id,
                'item_id' => $productData->item_id,
                'standardized_data' => $productData->standardized_data,
                'collected_at' => $productData->last_collected_at,
                'code' => $productData->code,
                'title' => $productData->title,
                'product_image' => $productData->product_image,
                'price' => $productData->price,
                'lowest_price' => $productData->lowest_price,
                'highest_price' => $productData->highest_price,
                'min_hand_price' => $productData->min_hand_price,
                'max_hand_price' => $productData->max_hand_price,
                'stock' => $productData->stock,
                'sales' => $productData->sales,
                'comment_count' => $productData->comment_count,
                'state' => $productData->state,
                'has_sku' => $productData->has_sku,
                'item_type' => $productData->item_type,
                'category_id' => $productData->category_id,
                'category_path' => $productData->category_path,
                'shop_id' => $productData->shop_id,
                'shop_name' => $productData->shop_name,
                'props' => $productData->props,
                'promotion' => $productData->promotion,
                'delivery_location' => $productData->delivery_location,
                'product_url' => $productData->product_url,
            ];

            $productDataHistory = ProductDataHistory::create($historyData);

            Log::info('商品历史数据已保存', [
                'product_data_history_id' => $productDataHistory->id,
                'product_data_id' => $productData->id
            ]);

            // 保存SKU历史数据
            if (!empty($skuFields)) {
                // 获取监控任务中的官方指导价配置
                $officialGuidePriceFromTask = $this->getOfficialGuidePriceForProduct($monitoringTaskId, $productId);

                $skuHistoryData = array_map(function ($sku) use ($productDataHistory, $safeNumeric, $officialGuidePriceFromTask) {
                    // Re-implement deviation rate calculation logic here for historical accuracy
                    $price = $safeNumeric($sku['price'] ?? 0);
                    $subPrice = $safeNumeric($sku['sub_price'] ?? $sku['promotion_price'] ?? null);
                    $officialGuidePrice = $safeNumeric($officialGuidePriceFromTask);

                    $promoRate = null;
                    if ($price > 0) {
                        $promoSubPrice = $subPrice ?? $price;
                        $promoRate = round((($price - $promoSubPrice) / $price) * 100, 2);
                    }

                    $channelRate = null;
                    if ($officialGuidePrice > 0) {
                        $channelSubPrice = $subPrice ?? $price;
                        if (!$channelSubPrice || $channelSubPrice <= 0) {
                            $channelSubPrice = $price ?? 0;
                        }
                        if ($channelSubPrice > 0) {
                            $channelRate = round((($officialGuidePrice - $channelSubPrice) / $officialGuidePrice) * 100, 2);
                        }
                    }

                    return [
                        'product_data_history_id' => $productDataHistory->id,
                        'sku_id' => $sku['sku_id'] ?? null,
                        'name' => $sku['name'] ?? $sku['sku_name'] ?? 'SKU-' . ($sku['sku_id'] ?? 'unknown'),
                        'price' => $price,
                        'sub_price' => $subPrice,
                        'sub_price_title' => $sku['sub_price_title'] ?? null,
                        'quantity' => $safeNumeric($sku['stock'] ?? 0),
                        'image_url' => $sku['image_url'] ?? null,
                        'official_guide_price' => $officialGuidePrice,
                        'promotion_deviation_rate' => $promoRate,
                        'channel_price_deviation_rate' => $channelRate,
                        'collected_at' => $productDataHistory->collected_at,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }, $skuFields);

                ProductSkuHistory::insert($skuHistoryData);

                Log::info('SKU历史数据已保存', [
                    'product_data_history_id' => $productDataHistory->id,
                    'sku_count' => count($skuHistoryData)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('保存历史数据失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 分发预警检查任务到队列
     *
     * @param ProductData $productData
     * @return void
     */
    private function dispatchAlertChecking(ProductData $productData): void
    {
        try {
            // **修复：使用默认队列而不是指定特定队列名**
            ProcessAlertChecking::dispatch($productData->id, $this->collectionBatchId)
                ->delay(now()->addSeconds(1)); // 延迟1秒执行，确保数据已完全保存

            Log::debug('预警检查任务已分发到队列', [
                'product_data_id' => $productData->id,
                'collection_batch_id' => $this->collectionBatchId,
                'queue_connection' => config('queue.default'),
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警检查任务失败', [
                'product_data_id' => $productData->id,
                'collection_batch_id' => $this->collectionBatchId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 统一处理异常
     * @param \Exception $e
     * @param int $dataSourceId
     * @param string|array|null $itemId
     */
    private function handleException(\Exception $e, int $dataSourceId, $itemId = null): void
    {
        // 修复：正确提取productId，确保类型安全
        $productId = null;
        if (is_array($itemId) && isset($itemId['product_id'])) {
            $productId = (string)$itemId['product_id'];
        } elseif (is_string($itemId) || is_numeric($itemId)) {
            $productId = (string)$itemId;
        }

        Log::critical('数据源任务最终失败', [
            'data_source_id' => $dataSourceId,
            'monitoring_task_id' => $this->monitoringTaskId ?? null,
            'item_id' => $itemId, // 记录原始的itemId结构
            'product_id' => $productId, // 记录解析后的ID
            'exception' => $e->getMessage(),
        ]);

        if ($productId && $this->monitoringTaskId) {
            // 只有当monitoring_task_id不为null时才更新特定商品采集状态
            try {
                ProductData::updateOrCreate(
                    [
                        'item_id' => $productId,
                        'monitoring_task_id' => $this->monitoringTaskId,
                    ],
                    [
                        'last_collection_status' => 'failed',
                        'last_collection_error' => $e->getMessage(),
                        'last_collected_at' => now(),
                    ]
                );
            } catch (\Exception $dbException) {
                Log::error('更新商品采集状态失败', [
                    'item_id' => $productId,
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'error' => $dbException->getMessage()
                ]);
            }
        }
    }

    /**
     * 分发任务分组采集完成检查任务到队列
     *
     * @return void
     */
    private function dispatchTaskGroupCollectionComplete(): void
    {
        try {
            // 使用Redis缓存确保每个collection_batch_id只分发一次任务
            $cacheKey = "task_group_completion_dispatched_{$this->collectionBatchId}";
            
            // 检查是否已经分发过这个批次的完成检查任务
            if (\Illuminate\Support\Facades\Cache::has($cacheKey)) {
                Log::debug('任务分组采集完成检查任务已经分发过，跳过重复分发', [
                    'monitoring_task_id' => $this->monitoringTaskId,
                    'collection_batch_id' => $this->collectionBatchId,
                ]);
                return;
            }

            // 设置缓存标记，防止重复分发（有效期4小时）
            \Illuminate\Support\Facades\Cache::put($cacheKey, true, now()->addHours(4));

            // **修复：使用默认队列而不是指定特定队列名**
            ProcessTaskGroupCollectionComplete::dispatch($this->monitoringTaskId, $this->collectionBatchId)
                ->delay(now()->addSeconds(30)); // 延迟30秒执行，确保所有预警检查都已完成

            Log::info('任务分组采集完成检查任务已分发到队列', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
                'cache_key' => $cacheKey,
                'queue_connection' => config('queue.default'),
            ]);
        } catch (\Exception $e) {
            Log::error('分发任务分组采集完成检查任务失败', [
                'monitoring_task_id' => $this->monitoringTaskId,
                'collection_batch_id' => $this->collectionBatchId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 获取有效价格：优先使用subPrice，如果subPrice为0或空则使用price
     */
    private function getEffectivePrice($productFields)
    {
        // 首先尝试subPrice
        $subPrice = $productFields['subPrice'] ?? $productFields['sub_price'] ?? null;
        if ($subPrice !== null && $subPrice !== '' && (float)$subPrice > 0) {
            return $subPrice;
        }

        // 如果subPrice无效，使用price
        return $productFields['price'] ?? null;
    }

    /**
     * 采集竞品数据
     *
     * @param DataSource $dataSource 数据源
     * @param array $targetProducts 目标商品列表
     * @param array $additionalParams 额外参数
     * @return array
     * @throws \Exception
     */
    public function collectCompetitorData(DataSource $dataSource, array $targetProducts = [], array $additionalParams = []): array
    {
        Log::info('开始采集竞品数据', [
            'data_source_id' => $dataSource->id,
            'target_products_count' => count($targetProducts),
            'additional_params' => $additionalParams
        ]);

        $collectedData = [];

        try {
            // 如果没有指定目标商品，使用关键词搜索
            if (empty($targetProducts)) {
                $keywords = $additionalParams['competitor_keywords'] ?? [];
                if (!empty($keywords)) {
                    $collectedData = $this->searchCompetitorProducts($dataSource, $keywords, $additionalParams);
                }
            } else {
                // 采集指定的目标商品
                foreach ($targetProducts as $productId) {
                    try {
                        $productData = $this->collectAndStandardize(
                            $dataSource->id,
                            $productId,
                            $additionalParams
                        );

                        if (!empty($productData)) {
                            $collectedData[] = $productData;
                        }
                    } catch (\Exception $e) {
                        Log::error('采集单个竞品商品失败', [
                            'product_id' => $productId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }

            Log::info('竞品数据采集完成', [
                'data_source_id' => $dataSource->id,
                'collected_count' => count($collectedData)
            ]);

            return $collectedData;

        } catch (\Exception $e) {
            Log::error('竞品数据采集失败', [
                'data_source_id' => $dataSource->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 通过关键词搜索竞品商品
     *
     * @param DataSource $dataSource
     * @param array $keywords
     * @param array $additionalParams
     * @return array
     */
    private function searchCompetitorProducts(DataSource $dataSource, array $keywords, array $additionalParams = []): array
    {
        $searchResults = [];

        foreach ($keywords as $keyword) {
            try {
                Log::info('搜索竞品关键词', [
                    'keyword' => $keyword,
                    'data_source_id' => $dataSource->id
                ]);

                // 构建搜索请求参数
                $searchParams = array_merge($additionalParams, [
                    'keyword' => $keyword,
                    'search_type' => 'competitor',
                    'limit' => $additionalParams['search_limit'] ?? 20
                ]);

                // 调用数据源API进行搜索
                $response = $this->callDataSourceApi($dataSource, 'search', $searchParams);

                if (!empty($response['products'])) {
                    foreach ($response['products'] as $product) {
                        // 对搜索到的商品进行详细数据采集
                        try {
                            $detailData = $this->collectAndStandardize(
                                $dataSource->id,
                                $product['item_id'],
                                $additionalParams
                            );

                            if (!empty($detailData)) {
                                $searchResults[] = $detailData;
                            }
                        } catch (\Exception $e) {
                            Log::warning('采集搜索结果商品详情失败', [
                                'item_id' => $product['item_id'],
                                'keyword' => $keyword,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }

            } catch (\Exception $e) {
                Log::error('关键词搜索失败', [
                    'keyword' => $keyword,
                    'data_source_id' => $dataSource->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $searchResults;
    }

    /**
     * 调用数据源API
     *
     * @param DataSource $dataSource
     * @param string $endpoint
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function callDataSourceApi(DataSource $dataSource, string $endpoint, array $params = []): array
    {
        $config = $dataSource->config;
        $baseUrl = $config['api_url'] ?? '';

        if (empty($baseUrl)) {
            throw new \Exception('数据源API地址未配置');
        }

        $url = rtrim($baseUrl, '/') . '/' . ltrim($endpoint, '/');

        // 构建请求头
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        // 添加认证信息
        if (!empty($config['api_key'])) {
            $headers['Authorization'] = 'Bearer ' . $config['api_key'];
        }

        // 发送HTTP请求
        $response = Http::withHeaders($headers)
            ->timeout(60)
            ->post($url, $params);

        if (!$response->successful()) {
            throw new \Exception('数据源API调用失败: ' . $response->body());
        }

        return $response->json();
    }
}