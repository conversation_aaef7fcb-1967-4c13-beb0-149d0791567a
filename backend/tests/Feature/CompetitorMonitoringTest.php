<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\CompetitorMonitoringTask;
use App\Models\CompetitorProductData;
use App\Models\CompetitorProductSku;
use App\Models\User;
use Laravel\Sanctum\Sanctum;

class CompetitorMonitoringTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        
        // 使用Sanctum认证
        Sanctum::actingAs($this->user);
    }

    /**
     * 测试创建竞品监测任务
     */
    public function test_can_create_competitor_monitoring_task()
    {
        $taskData = [
            'name' => '测试竞品监测任务',
            'description' => '这是一个测试任务',
            'status' => 'active',
            'collection_frequency' => 'daily',
            'guide_prices' => [
                'min_price' => 100.00,
                'max_price' => 500.00,
                'target_price' => 300.00
            ],
            'collection_settings' => [
                'data_sources' => ['taobao', 'tmall'],
                'keywords' => ['手机', '智能手机'],
                'categories' => ['电子产品']
            ]
        ];

        $response = $this->postJson('/api/competitor-monitoring-tasks', $taskData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'status',
                        'collection_frequency',
                        'guide_prices',
                        'collection_settings',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $this->assertDatabaseHas('competitor_monitoring_tasks', [
            'name' => '测试竞品监测任务',
            'description' => '这是一个测试任务',
            'status' => 'active'
        ]);
    }

    /**
     * 测试获取竞品监测任务列表
     */
    public function test_can_get_competitor_monitoring_tasks()
    {
        // 创建测试任务
        CompetitorMonitoringTask::factory()->count(3)->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->getJson('/api/competitor-monitoring-tasks');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'description',
                                'status',
                                'collection_frequency',
                                'created_at',
                                'updated_at'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total'
                    ]
                ]);
    }

    /**
     * 测试获取单个竞品监测任务
     */
    public function test_can_get_single_competitor_monitoring_task()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->getJson("/api/competitor-monitoring-tasks/{$task->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'status',
                        'collection_frequency',
                        'guide_prices',
                        'collection_settings',
                        'created_at',
                        'updated_at'
                    ]
                ]);
    }

    /**
     * 测试更新竞品监测任务
     */
    public function test_can_update_competitor_monitoring_task()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $updateData = [
            'name' => '更新后的任务名称',
            'description' => '更新后的描述',
            'status' => 'paused'
        ];

        $response = $this->putJson("/api/competitor-monitoring-tasks/{$task->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'status'
                    ]
                ]);

        $this->assertDatabaseHas('competitor_monitoring_tasks', [
            'id' => $task->id,
            'name' => '更新后的任务名称',
            'description' => '更新后的描述',
            'status' => 'paused'
        ]);
    }

    /**
     * 测试删除竞品监测任务
     */
    public function test_can_delete_competitor_monitoring_task()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->deleteJson("/api/competitor-monitoring-tasks/{$task->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '任务删除成功'
                ]);

        $this->assertSoftDeleted('competitor_monitoring_tasks', [
            'id' => $task->id
        ]);
    }

    /**
     * 测试启动竞品监测任务
     */
    public function test_can_start_competitor_monitoring_task()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id,
            'status' => 'paused'
        ]);

        $response = $this->postJson("/api/competitor-monitoring-tasks/{$task->id}/start");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '任务启动成功'
                ]);

        $this->assertDatabaseHas('competitor_monitoring_tasks', [
            'id' => $task->id,
            'status' => 'active'
        ]);
    }

    /**
     * 测试暂停竞品监测任务
     */
    public function test_can_pause_competitor_monitoring_task()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/competitor-monitoring-tasks/{$task->id}/pause");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '任务暂停成功'
                ]);

        $this->assertDatabaseHas('competitor_monitoring_tasks', [
            'id' => $task->id,
            'status' => 'paused'
        ]);
    }

    /**
     * 测试获取竞品分析核心指标
     */
    public function test_can_get_competitor_analytics_core_metrics()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        // 创建一些测试数据
        $productData = CompetitorProductData::factory()->create([
            'task_id' => $task->id
        ]);

        CompetitorProductSku::factory()->count(3)->create([
            'product_data_id' => $productData->id
        ]);

        $response = $this->getJson('/api/competitor/analytics/core-metrics', [
            'task_id' => $task->id,
            'date_range' => 30
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'promotion_strategy_tendency',
                        'comprehensive_discount_rate',
                        'overall_promotion_intensity',
                        'comprehensive_deviation_rate',
                        'min_price_deviation_rate',
                        'max_price_deviation_rate',
                        'price_trend_slope'
                    ]
                ]);
    }

    /**
     * 测试获取价格趋势图表数据
     */
    public function test_can_get_price_trend_chart_data()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->getJson('/api/competitor/analytics/price-trend-chart', [
            'task_id' => $task->id,
            'period' => 30
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    /**
     * 测试获取促销分布数据
     */
    public function test_can_get_promotion_distribution_data()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->getJson('/api/competitor/analytics/promotion-distribution', [
            'task_id' => $task->id,
            'date_range' => 30
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    /**
     * 测试获取类目价格热力图数据
     */
    public function test_can_get_category_price_heatmap_data()
    {
        $task = CompetitorMonitoringTask::factory()->create([
            'created_by' => $this->user->id
        ]);

        $response = $this->getJson('/api/competitor/analytics/category-price-heatmap', [
            'task_id' => $task->id,
            'date_range' => 30
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'heatmap_data',
                        'categories',
                        'price_ranges'
                    ]
                ]);
    }
}
