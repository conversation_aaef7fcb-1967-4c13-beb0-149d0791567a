<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // 运行角色权限种子文件
        $this->call([
            RolePermissionSeeder::class,
        ]);

        // Create test user if not exists
        $user = \App\Models\User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Test User',
            'password' => bcrypt('password'),
        ]);

        // Create test data source if not exists
        $dataSource = \App\Models\DataSource::firstOrCreate([
            'name' => '测试数据源',
            'owner_id' => $user->id,
        ], [
            'type' => 'api',
            'config' => ['url' => 'https://api.example.com'],
            'status' => 1
        ]);

        // Create test competitor monitoring task
        \App\Models\CompetitorMonitoringTask::firstOrCreate([
            'name' => '手机竞品价格监控',
            'user_id' => $user->id,
        ], [
            'description' => '监控主流手机品牌的价格变化和促销策略',
            'data_source_id' => $dataSource->id,
            'target_products' => ['12345', '67890'],
            'competitor_keywords' => ['iPhone', '华为', '小米'],
            'monitor_fields' => ['price', 'promotion', 'stock_status'],
            'frequency_type' => 'interval',
            'frequency_value' => 360,
            'our_guide_price_avg' => 3999.00,
            'status' => 'running',
            'last_run_at' => now()->subHours(2),
            'next_run_at' => now()->addHours(4)
        ]);
    }
}
