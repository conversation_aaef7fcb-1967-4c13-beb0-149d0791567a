<?php

namespace Database\Factories;

use App\Models\CompetitorProductSku;
use App\Models\CompetitorProductData;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompetitorProductSku>
 */
class CompetitorProductSkuFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompetitorProductSku::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $originalPrice = $this->faker->randomFloat(2, 50, 800);
        $discountRate = $this->faker->randomFloat(2, 0, 0.4);
        $subPrice = $originalPrice * (1 - $discountRate);
        
        return [
            'product_data_id' => CompetitorProductData::factory(),
            'sku_id' => $this->faker->unique()->bothify('SKU-########'),
            'sku_name' => $this->faker->words(2, true),
            'original_price' => $originalPrice,
            'sub_price' => $subPrice,
            'discount_rate' => $discountRate,
            'stock_quantity' => $this->faker->numberBetween(0, 1000),
            'sales_volume' => $this->faker->numberBetween(0, 5000),
            'sku_attributes' => [
                'color' => $this->faker->optional()->colorName(),
                'size' => $this->faker->optional()->randomElement(['S', 'M', 'L', 'XL']),
                'style' => $this->faker->optional()->word(),
                'material' => $this->faker->optional()->word(),
            ],
            'promotion_info' => [
                'promotion_type' => $this->faker->randomElement(['满减', '折扣', '秒杀', '拼团', '无促销']),
                'promotion_price' => $this->faker->optional()->randomFloat(2, 30, $subPrice),
                'promotion_start_time' => $this->faker->optional()->dateTimeBetween('-7 days', 'now'),
                'promotion_end_time' => $this->faker->optional()->dateTimeBetween('now', '+7 days'),
                'promotion_description' => $this->faker->optional()->sentence(),
            ],
            'availability_status' => $this->faker->randomElement(['in_stock', 'out_of_stock', 'pre_order', 'discontinued']),
            'shipping_info' => [
                'shipping_fee' => $this->faker->randomFloat(2, 0, 20),
                'free_shipping_threshold' => $this->faker->optional()->randomFloat(2, 50, 200),
                'estimated_delivery_days' => $this->faker->numberBetween(1, 15),
                'shipping_regions' => $this->faker->randomElements(['全国', '华东', '华南', '华北'], 2),
            ],
            'competitor_ranking' => [
                'price_rank' => $this->faker->numberBetween(1, 50),
                'sales_rank' => $this->faker->numberBetween(1, 50),
                'popularity_rank' => $this->faker->numberBetween(1, 50),
            ],
            'collected_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the SKU is in stock.
     */
    public function inStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'availability_status' => 'in_stock',
            'stock_quantity' => $this->faker->numberBetween(10, 1000),
        ]);
    }

    /**
     * Indicate that the SKU is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'availability_status' => 'out_of_stock',
            'stock_quantity' => 0,
        ]);
    }

    /**
     * Indicate that the SKU is on promotion.
     */
    public function onPromotion(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $attributes['original_price'];
            $discountRate = $this->faker->randomFloat(2, 0.1, 0.5);
            $subPrice = $originalPrice * (1 - $discountRate);
            $promotionPrice = $subPrice * $this->faker->randomFloat(2, 0.8, 0.95);
            
            return [
                'discount_rate' => $discountRate,
                'sub_price' => $subPrice,
                'promotion_info' => [
                    'promotion_type' => $this->faker->randomElement(['满减', '折扣', '秒杀', '拼团']),
                    'promotion_price' => $promotionPrice,
                    'promotion_start_time' => $this->faker->dateTimeBetween('-3 days', 'now'),
                    'promotion_end_time' => $this->faker->dateTimeBetween('now', '+7 days'),
                    'promotion_description' => $this->faker->sentence(),
                ],
            ];
        });
    }

    /**
     * Indicate that the SKU has no promotion.
     */
    public function noPromotion(): static
    {
        return $this->state(fn (array $attributes) => [
            'promotion_info' => [
                'promotion_type' => '无促销',
                'promotion_price' => null,
                'promotion_start_time' => null,
                'promotion_end_time' => null,
                'promotion_description' => null,
            ],
        ]);
    }

    /**
     * Indicate that the SKU is high-priced.
     */
    public function highPriced(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $this->faker->randomFloat(2, 500, 2000);
            $discountRate = $attributes['discount_rate'] ?? 0;
            
            return [
                'original_price' => $originalPrice,
                'sub_price' => $originalPrice * (1 - $discountRate),
            ];
        });
    }

    /**
     * Indicate that the SKU is low-priced.
     */
    public function lowPriced(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $this->faker->randomFloat(2, 10, 50);
            $discountRate = $attributes['discount_rate'] ?? 0;
            
            return [
                'original_price' => $originalPrice,
                'sub_price' => $originalPrice * (1 - $discountRate),
            ];
        });
    }

    /**
     * Indicate that the SKU is popular (high sales).
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'sales_volume' => $this->faker->numberBetween(1000, 10000),
            'competitor_ranking' => array_merge($attributes['competitor_ranking'] ?? [], [
                'sales_rank' => $this->faker->numberBetween(1, 10),
                'popularity_rank' => $this->faker->numberBetween(1, 10),
            ]),
        ]);
    }

    /**
     * Indicate that the SKU has free shipping.
     */
    public function freeShipping(): static
    {
        return $this->state(fn (array $attributes) => [
            'shipping_info' => array_merge($attributes['shipping_info'] ?? [], [
                'shipping_fee' => 0,
                'free_shipping_threshold' => null,
            ]),
        ]);
    }

    /**
     * Indicate that the SKU has fast delivery.
     */
    public function fastDelivery(): static
    {
        return $this->state(fn (array $attributes) => [
            'shipping_info' => array_merge($attributes['shipping_info'] ?? [], [
                'estimated_delivery_days' => $this->faker->numberBetween(1, 3),
            ]),
        ]);
    }

    /**
     * Configure the SKU for electronics category.
     */
    public function electronics(): static
    {
        return $this->state(fn (array $attributes) => [
            'sku_attributes' => [
                'color' => $this->faker->randomElement(['黑色', '白色', '蓝色', '红色', '金色']),
                'storage' => $this->faker->randomElement(['64GB', '128GB', '256GB', '512GB', '1TB']),
                'memory' => $this->faker->randomElement(['4GB', '6GB', '8GB', '12GB', '16GB']),
                'screen_size' => $this->faker->randomFloat(1, 5.0, 7.0) . '英寸',
            ],
        ]);
    }

    /**
     * Configure the SKU for fashion category.
     */
    public function fashion(): static
    {
        return $this->state(fn (array $attributes) => [
            'sku_attributes' => [
                'color' => $this->faker->colorName(),
                'size' => $this->faker->randomElement(['XS', 'S', 'M', 'L', 'XL', 'XXL']),
                'material' => $this->faker->randomElement(['棉', '聚酯纤维', '羊毛', '丝绸', '牛仔布']),
                'style' => $this->faker->randomElement(['休闲', '商务', '运动', '时尚', '复古']),
            ],
        ]);
    }

    /**
     * Configure the SKU with specific color.
     */
    public function withColor(string $color): static
    {
        return $this->state(fn (array $attributes) => [
            'sku_attributes' => array_merge($attributes['sku_attributes'] ?? [], [
                'color' => $color,
            ]),
        ]);
    }

    /**
     * Configure the SKU with specific size.
     */
    public function withSize(string $size): static
    {
        return $this->state(fn (array $attributes) => [
            'sku_attributes' => array_merge($attributes['sku_attributes'] ?? [], [
                'size' => $size,
            ]),
        ]);
    }

    /**
     * Indicate that the SKU was collected recently.
     */
    public function recentlyCollected(): static
    {
        return $this->state(fn (array $attributes) => [
            'collected_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }

    /**
     * Indicate that the SKU has high ranking.
     */
    public function topRanked(): static
    {
        return $this->state(fn (array $attributes) => [
            'competitor_ranking' => [
                'price_rank' => $this->faker->numberBetween(1, 5),
                'sales_rank' => $this->faker->numberBetween(1, 5),
                'popularity_rank' => $this->faker->numberBetween(1, 5),
            ],
        ]);
    }

    /**
     * Indicate that the SKU has low stock.
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => $this->faker->numberBetween(1, 10),
            'availability_status' => 'in_stock',
        ]);
    }
}
