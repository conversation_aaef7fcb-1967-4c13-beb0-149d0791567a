<?php

namespace Database\Factories;

use App\Models\CompetitorMonitoringTask;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompetitorMonitoringTask>
 */
class CompetitorMonitoringTaskFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompetitorMonitoringTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['active', 'paused', 'stopped']),
            'collection_frequency' => $this->faker->randomElement(['hourly', 'daily', 'weekly']),
            'guide_prices' => [
                'min_price' => $this->faker->randomFloat(2, 50, 200),
                'max_price' => $this->faker->randomFloat(2, 300, 1000),
                'target_price' => $this->faker->randomFloat(2, 200, 500),
            ],
            'collection_settings' => [
                'data_sources' => $this->faker->randomElements(['taobao', 'tmall', 'jd', 'pdd'], 2),
                'keywords' => $this->faker->words(3),
                'categories' => $this->faker->randomElements(['电子产品', '服装', '家居', '美妆'], 2),
                'max_products' => $this->faker->numberBetween(50, 200),
                'collection_interval' => $this->faker->numberBetween(1, 24),
            ],
            'alert_rules' => [
                'price_deviation_threshold' => $this->faker->randomFloat(2, 0.1, 0.5),
                'promotion_intensity_threshold' => $this->faker->randomFloat(2, 0.3, 0.8),
                'enable_email_alerts' => $this->faker->boolean(),
                'enable_sms_alerts' => $this->faker->boolean(),
            ],
            'created_by' => User::factory(),
            'last_run_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'next_run_at' => $this->faker->dateTimeBetween('now', '+1 day'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the task is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the task is paused.
     */
    public function paused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paused',
        ]);
    }

    /**
     * Indicate that the task is stopped.
     */
    public function stopped(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'stopped',
        ]);
    }

    /**
     * Indicate that the task runs daily.
     */
    public function daily(): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_frequency' => 'daily',
        ]);
    }

    /**
     * Indicate that the task runs hourly.
     */
    public function hourly(): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_frequency' => 'hourly',
        ]);
    }

    /**
     * Indicate that the task runs weekly.
     */
    public function weekly(): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_frequency' => 'weekly',
        ]);
    }

    /**
     * Configure the task for electronics category.
     */
    public function electronics(): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_settings' => array_merge($attributes['collection_settings'] ?? [], [
                'categories' => ['电子产品', '数码配件'],
                'keywords' => ['手机', '电脑', '平板', '耳机'],
            ]),
            'guide_prices' => [
                'min_price' => 100.00,
                'max_price' => 5000.00,
                'target_price' => 2000.00,
            ],
        ]);
    }

    /**
     * Configure the task for fashion category.
     */
    public function fashion(): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_settings' => array_merge($attributes['collection_settings'] ?? [], [
                'categories' => ['服装', '鞋靴', '箱包'],
                'keywords' => ['连衣裙', '运动鞋', '手提包'],
            ]),
            'guide_prices' => [
                'min_price' => 50.00,
                'max_price' => 1000.00,
                'target_price' => 300.00,
            ],
        ]);
    }

    /**
     * Configure the task with high alert sensitivity.
     */
    public function highAlertSensitivity(): static
    {
        return $this->state(fn (array $attributes) => [
            'alert_rules' => array_merge($attributes['alert_rules'] ?? [], [
                'price_deviation_threshold' => 0.1,
                'promotion_intensity_threshold' => 0.3,
                'enable_email_alerts' => true,
                'enable_sms_alerts' => true,
            ]),
        ]);
    }

    /**
     * Configure the task with low alert sensitivity.
     */
    public function lowAlertSensitivity(): static
    {
        return $this->state(fn (array $attributes) => [
            'alert_rules' => array_merge($attributes['alert_rules'] ?? [], [
                'price_deviation_threshold' => 0.5,
                'promotion_intensity_threshold' => 0.8,
                'enable_email_alerts' => false,
                'enable_sms_alerts' => false,
            ]),
        ]);
    }

    /**
     * Configure the task to collect from specific data sources.
     */
    public function fromSources(array $sources): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_settings' => array_merge($attributes['collection_settings'] ?? [], [
                'data_sources' => $sources,
            ]),
        ]);
    }

    /**
     * Configure the task with specific keywords.
     */
    public function withKeywords(array $keywords): static
    {
        return $this->state(fn (array $attributes) => [
            'collection_settings' => array_merge($attributes['collection_settings'] ?? [], [
                'keywords' => $keywords,
            ]),
        ]);
    }

    /**
     * Configure the task with recent activity.
     */
    public function recentlyActive(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_run_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'next_run_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
        ]);
    }

    /**
     * Configure the task as never run.
     */
    public function neverRun(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_run_at' => null,
            'next_run_at' => $this->faker->dateTimeBetween('now', '+1 day'),
        ]);
    }
}
