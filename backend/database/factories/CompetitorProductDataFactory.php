<?php

namespace Database\Factories;

use App\Models\CompetitorProductData;
use App\Models\CompetitorMonitoringTask;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompetitorProductData>
 */
class CompetitorProductDataFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompetitorProductData::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $originalPrice = $this->faker->randomFloat(2, 100, 1000);
        $discountRate = $this->faker->randomFloat(2, 0, 0.5);
        $competitorPrice = $originalPrice * (1 - $discountRate);
        
        return [
            'task_id' => CompetitorMonitoringTask::factory(),
            'product_name' => $this->faker->words(3, true),
            'product_url' => $this->faker->url(),
            'product_image' => $this->faker->imageUrl(400, 400, 'products'),
            'brand' => $this->faker->company(),
            'category_name' => $this->faker->randomElement(['电子产品', '服装', '家居', '美妆', '运动户外']),
            'category_id' => $this->faker->numberBetween(1, 100),
            'original_price' => $originalPrice,
            'competitor_price' => $competitorPrice,
            'discount_rate' => $discountRate,
            'promotion_type' => $this->faker->randomElement(['满减', '折扣', '秒杀', '拼团', '无促销']),
            'promotion_description' => $this->faker->optional()->sentence(),
            'sales_volume' => $this->faker->numberBetween(0, 10000),
            'rating' => $this->faker->randomFloat(1, 3.0, 5.0),
            'review_count' => $this->faker->numberBetween(0, 5000),
            'shop_name' => $this->faker->company() . '旗舰店',
            'shop_type' => $this->faker->randomElement(['官方旗舰店', '专营店', '普通店铺']),
            'data_source' => $this->faker->randomElement(['taobao', 'tmall', 'jd', 'pdd']),
            'collected_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'product_attributes' => [
                'color' => $this->faker->optional()->colorName(),
                'size' => $this->faker->optional()->randomElement(['S', 'M', 'L', 'XL']),
                'material' => $this->faker->optional()->word(),
                'weight' => $this->faker->optional()->randomFloat(2, 0.1, 10.0),
            ],
            'competitor_info' => [
                'competitor_id' => $this->faker->uuid(),
                'competitor_name' => $this->faker->company(),
                'market_share' => $this->faker->randomFloat(2, 0.01, 0.3),
                'brand_level' => $this->faker->randomElement(['一线', '二线', '三线', '其他']),
            ],
            'market_position' => [
                'price_rank' => $this->faker->numberBetween(1, 100),
                'sales_rank' => $this->faker->numberBetween(1, 100),
                'rating_rank' => $this->faker->numberBetween(1, 100),
            ],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the product is on promotion.
     */
    public function onPromotion(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $attributes['original_price'];
            $discountRate = $this->faker->randomFloat(2, 0.1, 0.6);
            
            return [
                'discount_rate' => $discountRate,
                'competitor_price' => $originalPrice * (1 - $discountRate),
                'promotion_type' => $this->faker->randomElement(['满减', '折扣', '秒杀', '拼团']),
                'promotion_description' => $this->faker->sentence(),
            ];
        });
    }

    /**
     * Indicate that the product has no promotion.
     */
    public function noPromotion(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_rate' => 0,
            'competitor_price' => $attributes['original_price'],
            'promotion_type' => '无促销',
            'promotion_description' => null,
        ]);
    }

    /**
     * Indicate that the product is high-priced.
     */
    public function highPriced(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $this->faker->randomFloat(2, 1000, 5000);
            $discountRate = $attributes['discount_rate'] ?? 0;
            
            return [
                'original_price' => $originalPrice,
                'competitor_price' => $originalPrice * (1 - $discountRate),
            ];
        });
    }

    /**
     * Indicate that the product is low-priced.
     */
    public function lowPriced(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $this->faker->randomFloat(2, 10, 100);
            $discountRate = $attributes['discount_rate'] ?? 0;
            
            return [
                'original_price' => $originalPrice,
                'competitor_price' => $originalPrice * (1 - $discountRate),
            ];
        });
    }

    /**
     * Indicate that the product is popular (high sales).
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'sales_volume' => $this->faker->numberBetween(5000, 50000),
            'rating' => $this->faker->randomFloat(1, 4.0, 5.0),
            'review_count' => $this->faker->numberBetween(1000, 10000),
        ]);
    }

    /**
     * Indicate that the product is from a specific data source.
     */
    public function fromSource(string $source): static
    {
        return $this->state(fn (array $attributes) => [
            'data_source' => $source,
        ]);
    }

    /**
     * Indicate that the product is from a specific category.
     */
    public function inCategory(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category_name' => $category,
        ]);
    }

    /**
     * Indicate that the product was collected recently.
     */
    public function recentlyCollected(): static
    {
        return $this->state(fn (array $attributes) => [
            'collected_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }

    /**
     * Indicate that the product was collected long ago.
     */
    public function oldCollection(): static
    {
        return $this->state(fn (array $attributes) => [
            'collected_at' => $this->faker->dateTimeBetween('-90 days', '-30 days'),
        ]);
    }

    /**
     * Configure the product for electronics category.
     */
    public function electronics(): static
    {
        return $this->state(fn (array $attributes) => [
            'category_name' => '电子产品',
            'product_attributes' => [
                'brand' => $this->faker->randomElement(['Apple', 'Samsung', 'Huawei', 'Xiaomi']),
                'model' => $this->faker->bothify('##??-####'),
                'color' => $this->faker->randomElement(['黑色', '白色', '蓝色', '红色']),
                'storage' => $this->faker->randomElement(['64GB', '128GB', '256GB', '512GB']),
                'screen_size' => $this->faker->randomFloat(1, 5.0, 7.0) . '英寸',
            ],
        ]);
    }

    /**
     * Configure the product for fashion category.
     */
    public function fashion(): static
    {
        return $this->state(fn (array $attributes) => [
            'category_name' => '服装',
            'product_attributes' => [
                'color' => $this->faker->colorName(),
                'size' => $this->faker->randomElement(['XS', 'S', 'M', 'L', 'XL', 'XXL']),
                'material' => $this->faker->randomElement(['棉', '聚酯纤维', '羊毛', '丝绸']),
                'style' => $this->faker->randomElement(['休闲', '商务', '运动', '时尚']),
                'season' => $this->faker->randomElement(['春', '夏', '秋', '冬']),
            ],
        ]);
    }

    /**
     * Configure the product with high rating.
     */
    public function highRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(1, 4.5, 5.0),
            'review_count' => $this->faker->numberBetween(500, 5000),
        ]);
    }

    /**
     * Configure the product with low rating.
     */
    public function lowRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(1, 1.0, 3.0),
            'review_count' => $this->faker->numberBetween(1, 100),
        ]);
    }
}
