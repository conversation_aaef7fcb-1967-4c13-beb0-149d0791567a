<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('competitor_product_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('competitor_monitoring_task_id')->comment('竞品监控任务ID');
            $table->foreign('competitor_monitoring_task_id', 'comp_prod_task_fk')->references('id')->on('competitor_monitoring_tasks')->onDelete('cascade');
            $table->string('item_id', 100)->comment('竞品商品唯一ID');
            $table->json('standardized_data')->comment('标准化后的数据');
            $table->timestamp('last_collected_at')->comment('最后采集时间');
            
            // 基本商品信息字段（从standardized_data中提取的常用字段，便于查询和索引）
            $table->integer('code')->nullable()->comment('状态码');
            $table->text('title')->nullable()->comment('商品标题');
            $table->string('product_image', 500)->nullable()->comment('商品主图URL');
            $table->decimal('price', 10, 2)->nullable()->comment('商品原价');
            $table->decimal('lowest_price', 10, 2)->nullable()->comment('最低到手价');
            $table->decimal('highest_price', 10, 2)->nullable()->comment('最高到手价');
            $table->decimal('min_hand_price', 10, 2)->nullable()->comment('最低到手价（subPrice最小值）');
            $table->decimal('max_hand_price', 10, 2)->nullable()->comment('最高到手价（subPrice最大值）');
            $table->integer('stock')->nullable()->comment('库存数量');
            $table->integer('sales')->nullable()->comment('销量');
            $table->integer('comment_count')->nullable()->comment('评论数');
            $table->tinyInteger('state')->default(1)->comment('商品状态：1-上架，0-下架');
            $table->tinyInteger('has_sku')->default(0)->comment('是否有SKU：1-是，0-否');
            $table->string('item_type', 50)->nullable()->comment('商品平台类型');
            $table->string('category_id', 100)->nullable()->comment('类目ID');
            $table->string('category_path', 500)->nullable()->comment('类目路径');
            $table->string('shop_id', 100)->nullable()->comment('店铺ID');
            $table->string('shop_name', 200)->nullable()->comment('店铺名称');
            $table->json('props')->nullable()->comment('商品属性');
            $table->json('promotion')->nullable()->comment('促销信息');
            $table->string('delivery_location', 100)->nullable()->comment('发货地');
            $table->string('product_url', 1000)->nullable()->comment('商品链接');
            
            // 竞品分析相关字段
            $table->decimal('our_guide_price', 10, 2)->nullable()->comment('我方指导价');
            $table->string('collection_batch_id', 100)->nullable()->comment('采集批次ID');
            
            $table->timestamps();

            // 索引
            $table->unique(['competitor_monitoring_task_id', 'item_id'], 'comp_prod_task_item_unique');
            $table->index('last_collected_at');
            $table->index('state');
            $table->index('category_id');
            $table->index('shop_id');
            $table->index('collection_batch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('competitor_product_data');
    }
};
