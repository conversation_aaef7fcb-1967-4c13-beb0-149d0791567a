<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('competitor_product_skus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('competitor_product_data_id')->comment('竞品商品数据ID');
            $table->foreign('competitor_product_data_id', 'comp_prod_sku_fk')->references('id')->on('competitor_product_data')->onDelete('cascade');
            $table->string('sku_id', 100)->comment('SKU唯一标识符');
            $table->string('name', 200)->nullable()->comment('SKU名称（规格组合）');
            $table->decimal('price', 10, 2)->nullable()->comment('SKU原价');
            $table->decimal('sub_price', 10, 2)->nullable()->comment('SKU券后价（实际到手价）');
            $table->string('sub_price_title', 100)->nullable()->comment('券后价名称');
            $table->integer('quantity')->nullable()->comment('SKU库存数量');
            $table->json('promotion')->nullable()->comment('SKU级别的优惠信息');
            
            // 竞品分析相关字段
            $table->decimal('our_guide_price', 10, 2)->nullable()->comment('我方指导价（SKU级别）');
            
            // 核心指标计算字段
            $table->decimal('promotion_strategy_score', 8, 4)->nullable()->comment('促销策略倾向得分');
            $table->decimal('comprehensive_discount_rate', 8, 4)->nullable()->comment('单品价格综合折扣率');
            $table->decimal('overall_promotion_intensity', 8, 4)->nullable()->comment('总体促销强度指数');
            $table->decimal('comprehensive_deviation_rate', 8, 4)->nullable()->comment('单品价格综合偏差率');
            $table->decimal('min_price_deviation_rate', 8, 4)->nullable()->comment('单品最低价偏差率');
            $table->decimal('max_price_deviation_rate', 8, 4)->nullable()->comment('单品最高价偏差率');
            $table->decimal('price_trend_slope', 8, 4)->nullable()->comment('竞品价格趋势斜率');
            
            $table->timestamps();

            // 索引
            $table->unique(['competitor_product_data_id', 'sku_id'], 'comp_prod_sku_unique');
            $table->index('price');
            $table->index('sub_price');
            $table->index('quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('competitor_product_skus');
    }
};
