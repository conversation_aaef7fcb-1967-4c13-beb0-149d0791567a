<template>
  <div class="competitor-metrics">
    <el-card class="metrics-card">
      <template #header>
        <div class="card-header">
          <span>竞品核心指标概览</span>
          <div class="header-actions">
            <el-select v-model="selectedTask" placeholder="选择监测任务" @change="loadMetrics">
              <el-option
                v-for="task in tasks"
                :key="task.id"
                :label="task.name"
                :value="task.id"
              />
            </el-select>
            <el-button type="primary" :icon="Refresh" @click="refreshMetrics" :loading="loading">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="metrics-content">
        <el-row :gutter="20">
          <!-- 促销策略倾向 -->
          <el-col :span="12">
            <div class="metric-item">
              <h4>促销策略倾向</h4>
              <div class="strategy-chart">
                <div v-if="metrics.promotion_strategy_tendency" class="strategy-list">
                  <div
                    v-for="(strategy, key) in metrics.promotion_strategy_tendency"
                    :key="key"
                    class="strategy-item"
                  >
                    <div class="strategy-name">{{ key }}</div>
                    <div class="strategy-stats">
                      <span class="count">{{ strategy.count }}次</span>
                      <span class="percentage">{{ strategy.percentage.toFixed(1) }}%</span>
                    </div>
                    <div class="strategy-bar">
                      <div
                        class="strategy-progress"
                        :style="{ width: strategy.percentage + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>
                <el-empty v-else description="暂无数据" />
              </div>
            </div>
          </el-col>

          <!-- 价格指标 -->
          <el-col :span="12">
            <div class="metric-item">
              <h4>价格相关指标</h4>
              <div class="price-metrics">
                <div class="metric-row">
                  <span class="metric-label">综合折扣率</span>
                  <span class="metric-value discount-rate">
                    {{ formatPercentage(metrics.comprehensive_discount_rate) }}
                  </span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">综合偏差率</span>
                  <span class="metric-value deviation-rate">
                    {{ formatPercentage(metrics.comprehensive_deviation_rate) }}
                  </span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">最低价偏差率</span>
                  <span class="metric-value min-deviation">
                    {{ formatPercentage(metrics.min_price_deviation_rate) }}
                  </span>
                </div>
                <div class="metric-row">
                  <span class="metric-label">最高价偏差率</span>
                  <span class="metric-value max-deviation">
                    {{ formatPercentage(metrics.max_price_deviation_rate) }}
                  </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 促销强度指数 -->
          <el-col :span="8">
            <div class="metric-item">
              <h4>总体促销强度指数</h4>
              <div class="intensity-display">
                <div class="intensity-circle">
                  <div class="intensity-value">
                    {{ formatPercentage(metrics.overall_promotion_intensity) }}
                  </div>
                  <div class="intensity-label">促销强度</div>
                </div>
                <div class="intensity-level">
                  <el-tag :type="getIntensityLevel(metrics.overall_promotion_intensity).type">
                    {{ getIntensityLevel(metrics.overall_promotion_intensity).label }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 价格趋势斜率 -->
          <el-col :span="8">
            <div class="metric-item">
              <h4>价格趋势斜率</h4>
              <div class="trend-display">
                <div class="trend-value">
                  {{ formatTrendSlope(metrics.price_trend_slope) }}
                </div>
                <div class="trend-direction">
                  <el-icon :class="getTrendDirection(metrics.price_trend_slope).class">
                    <component :is="getTrendDirection(metrics.price_trend_slope).icon" />
                  </el-icon>
                  <span>{{ getTrendDirection(metrics.price_trend_slope).label }}</span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 数据更新时间 -->
          <el-col :span="8">
            <div class="metric-item">
              <h4>数据状态</h4>
              <div class="data-status">
                <div class="update-time">
                  <span class="label">最后更新：</span>
                  <span class="time">{{ formatTime(lastUpdateTime) }}</span>
                </div>
                <div class="data-count">
                  <span class="label">监测商品：</span>
                  <span class="count">{{ productCount }}个</span>
                </div>
                <div class="task-status">
                  <span class="label">任务状态：</span>
                  <el-tag :type="taskStatus.type" size="small">
                    {{ taskStatus.label }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import competitorAnalyticsApi from '@/api/competitorAnalytics'
import competitorTaskApi from '@/api/competitorTask'

// 响应式数据
const loading = ref(false)
const selectedTask = ref('')
const tasks = ref([])
const lastUpdateTime = ref(null)
const productCount = ref(0)

const metrics = reactive({
  promotion_strategy_tendency: {},
  comprehensive_discount_rate: 0,
  overall_promotion_intensity: 0,
  comprehensive_deviation_rate: 0,
  min_price_deviation_rate: 0,
  max_price_deviation_rate: 0,
  price_trend_slope: 0
})

const taskStatus = computed(() => {
  // 这里可以根据实际任务状态返回不同的状态
  return {
    type: 'success',
    label: '运行中'
  }
})

// 方法
const loadTasks = async () => {
  try {
    const response = await competitorTaskApi.getTasks()
    tasks.value = response.data.data || []
    if (tasks.value.length > 0 && !selectedTask.value) {
      selectedTask.value = tasks.value[0].id
      await loadMetrics()
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  }
}

const loadMetrics = async () => {
  if (!selectedTask.value) return

  loading.value = true
  try {
    const response = await competitorAnalyticsApi.getCoreMetrics({
      task_id: selectedTask.value,
      date_range: 30
    })

    if (response.data.success) {
      Object.assign(metrics, response.data.data)
      lastUpdateTime.value = new Date()
      
      // 获取商品数量（这里可以调用其他API获取）
      productCount.value = 25 // 示例数据
    }
  } catch (error) {
    console.error('加载指标数据失败:', error)
    ElMessage.error('加载指标数据失败')
  } finally {
    loading.value = false
  }
}

const refreshMetrics = async () => {
  await loadMetrics()
  ElMessage.success('数据已刷新')
}

// 格式化方法
const formatPercentage = (value) => {
  if (value === null || value === undefined) return '--'
  return `${value.toFixed(2)}%`
}

const formatTrendSlope = (value) => {
  if (value === null || value === undefined) return '--'
  return value.toFixed(4)
}

const formatTime = (time) => {
  if (!time) return '--'
  return new Date(time).toLocaleString()
}

const getIntensityLevel = (intensity) => {
  if (intensity >= 0.8) return { type: 'danger', label: '高强度' }
  if (intensity >= 0.5) return { type: 'warning', label: '中强度' }
  if (intensity >= 0.2) return { type: 'info', label: '低强度' }
  return { type: '', label: '无促销' }
}

const getTrendDirection = (slope) => {
  if (slope > 0.1) return { icon: ArrowUp, class: 'trend-up', label: '上升趋势' }
  if (slope < -0.1) return { icon: ArrowDown, class: 'trend-down', label: '下降趋势' }
  return { icon: Minus, class: 'trend-stable', label: '趋势平稳' }
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.competitor-metrics {
  padding: 20px;
}

.metrics-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metrics-content {
  min-height: 400px;
}

.metric-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  height: 100%;
}

.metric-item h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

/* 促销策略样式 */
.strategy-list {
  max-height: 300px;
  overflow-y: auto;
}

.strategy-item {
  margin-bottom: 12px;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.strategy-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.strategy-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

.strategy-bar {
  height: 4px;
  background: #e4e7ed;
  border-radius: 2px;
  overflow: hidden;
}

.strategy-progress {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: width 0.3s ease;
}

/* 价格指标样式 */
.price-metrics {
  background: white;
  border-radius: 4px;
  padding: 16px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-row:last-child {
  border-bottom: none;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.metric-value {
  font-weight: 600;
  font-size: 16px;
}

.discount-rate { color: #67c23a; }
.deviation-rate { color: #e6a23c; }
.min-deviation { color: #f56c6c; }
.max-deviation { color: #909399; }

/* 促销强度样式 */
.intensity-display {
  text-align: center;
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.intensity-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto 16px;
  color: white;
}

.intensity-value {
  font-size: 24px;
  font-weight: bold;
}

.intensity-label {
  font-size: 12px;
  margin-top: 4px;
}

/* 趋势样式 */
.trend-display {
  text-align: center;
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.trend-value {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.trend-direction {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.trend-up { color: #f56c6c; }
.trend-down { color: #67c23a; }
.trend-stable { color: #909399; }

/* 数据状态样式 */
.data-status {
  background: white;
  border-radius: 4px;
  padding: 16px;
}

.data-status > div {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-status > div:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
}

.time, .count {
  font-weight: 500;
  color: #2c3e50;
}
</style>
