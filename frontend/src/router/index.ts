import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import NotFound from '../views/NotFound.vue'
import AppLayout from '../components/AppLayout.vue'

// 渠道价格监测
import ChannelPriceTaskManagement from '../views/ChannelPriceTaskManagement.vue'
import ChannelPriceDashboard from '../views/ChannelPriceDashboard.vue'
import ChannelPriceAlerts from '../views/ChannelPriceAlerts.vue'
import ProductHistoryDetail from '../views/ProductHistoryDetail.vue'

// 竞品动态监测
import CompetitorTaskManagement from '../views/CompetitorTaskManagement.vue'
import CompetitorDashboard from '../views/CompetitorDashboard.vue'
import CompetitorAlerts from '../views/CompetitorAlerts.vue'

// 相似同款查询
import SimilarLiveSearch from '../views/SimilarLiveSearch.vue'
import SimilarMonitoring from '../views/SimilarMonitoring.vue'
import SimilarDashboard from '../views/SimilarDashboard.vue'

// 系统管理
import DataSourceManagement from '../views/DataSourceManagement.vue'
import CreateDataSource from '../views/CreateDataSource.vue'
import UserManagement from '../views/UserManagement.vue'
import RoleManagement from '../views/RoleManagement.vue'
import SystemSettings from '../views/SystemSettings.vue'
import AuditLogs from '../views/AuditLogs.vue'

// 个人中心
import ProfileMessages from '../views/ProfileMessages.vue'
import ProfilePassword from '../views/ProfilePassword.vue'

// 兼容旧页面
import TaskManagement from '../views/TaskManagement.vue'
import TaskGroupManagement from '../views/TaskGroupManagement.vue'
import AlertCenter from '../views/AlertCenter.vue'

// 测试组件
import TestAlert from '../components/TestAlert.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Home',
        redirect: '/channel-price/tasks'
      },
      {
        path: '/home',
        name: 'HomePage',
        component: Home
      },
      {
        path: '/about',
        name: 'About',
        component: About
      },
      
      // 渠道价格监测
      {
        path: '/channel-price/tasks',
        name: 'ChannelPriceTaskManagement',
        component: ChannelPriceTaskManagement
      },
      {
        path: '/channel-price/dashboard',
        name: 'ChannelPriceDashboard',
        component: ChannelPriceDashboard
      },
      {
        path: '/channel-price/alerts',
        name: 'ChannelPriceAlerts',
        component: ChannelPriceAlerts
      },
      {
        path: '/channel-price/product-history/:taskId/:itemId/:historyId',
        name: 'ProductHistoryDetail',
        component: ProductHistoryDetail,
        props: true
      },
      
      // 竞品动态监测
      {
        path: '/competitor/tasks',
        name: 'CompetitorTaskManagement',
        component: CompetitorTaskManagement,
        meta: { title: '竞品任务管理' }
      },
      {
        path: '/competitor/dashboard',
        name: 'CompetitorDashboard',
        component: CompetitorDashboard,
        meta: { title: '竞品数据看板' }
      },
      {
        path: '/competitor/alerts',
        name: 'CompetitorAlerts',
        component: CompetitorAlerts,
        meta: { title: '竞品预警中心' }
      },
      
      // 相似同款查询
      {
        path: '/similar/live-search',
        name: 'SimilarLiveSearch',
        component: SimilarLiveSearch
      },
      {
        path: '/similar/monitoring',
        name: 'SimilarMonitoring',
        component: SimilarMonitoring
      },
      {
        path: '/similar/dashboard',
        name: 'SimilarDashboard',
        component: SimilarDashboard
      },
      
      // 系统管理 - 仅管理员可访问
      {
        path: '/system/data-sources',
        name: 'DataSourceManagement',
        component: DataSourceManagement,
        meta: { requiresRole: 'admin' }
      },
      {
        path: '/system/data-sources/create',
        name: 'CreateDataSource',
        component: CreateDataSource,
        meta: { requiresRole: 'admin' }
      },
      {
        path: '/system/users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresRole: 'admin' }
      },
      {
        path: '/system/roles',
        name: 'RoleManagement',
        component: RoleManagement,
        meta: { requiresRole: 'admin' }
      },
      {
        path: '/system/settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { requiresRole: 'admin' }
      },
      {
        path: '/system/audit-logs',
        name: 'AuditLogs',
        component: AuditLogs,
        meta: { requiresRole: 'admin' }
      },
      
      // 个人中心
      {
        path: '/profile/messages',
        name: 'ProfileMessages',
        component: ProfileMessages
      },
      {
        path: '/profile/password',
        name: 'ProfilePassword',
        component: ProfilePassword
      },

      // 兼容旧路由 - 重定向到新路由
      {
        path: '/data-sources',
        redirect: '/system/data-sources'
      },
      {
        path: '/data-sources/create',
        redirect: '/system/data-sources/create'
      },
      {
        path: '/monitoring/tasks',
        redirect: '/channel-price/tasks'
      },
      {
        path: '/monitoring/dashboard',
        redirect: '/channel-price/dashboard'
      },
      {
        path: '/analytics/competitors',
        redirect: '/competitor/dashboard'
      },
      {
        path: '/alerts',
        redirect: '/channel-price/alerts'
      },
      
      // 保留旧页面路由以防万一
      {
        path: '/task-management',
        name: 'TaskManagement',
        component: TaskManagement
      },
      {
        path: '/task-groups',
        name: 'TaskGroupManagement',
        component: TaskGroupManagement
      },
      {
        path: '/alert-center',
        name: 'AlertCenter',
        component: AlertCenter
      },

      // 测试页面
      {
        path: '/test-alert',
        name: 'TestAlert',
        component: TestAlert
      }
    ]
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 如果目标路由需要认证
  if (to.meta.requiresAuth !== false) {
    // 初始化认证状态（如果还没有初始化）
    if (!authStore.user && authStore.token) {
      await authStore.checkAuth()
    }
    
    // 检查是否已登录
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.requiresRole) {
      const requiredRole = to.meta.requiresRole as string
      if (requiredRole === 'admin' && !authStore.isAdmin) {
        ElMessage.error('您没有权限访问此页面')
        next('/')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页或注册页，重定向到首页
  if ((to.path === '/login' || to.path === '/register') && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router 