import apiClient from './apiClient'

/**
 * 竞品分析API服务
 */
const competitorAnalyticsApi = {
  /**
   * 获取竞品核心指标概览
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getCoreMetrics(params = {}) {
    return apiClient.get('/competitor/analytics/core-metrics', { params })
  },

  /**
   * 获取促销策略倾向分析
   * @param {object} params - 查询参数 (task_id, date_range, category_id)
   * @returns {Promise}
   */
  getPromotionStrategyTendency(params = {}) {
    return apiClient.get('/competitor/analytics/promotion-strategy-tendency', { params })
  },

  /**
   * 获取单品价格综合折扣率
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getComprehensiveDiscountRate(params = {}) {
    return apiClient.get('/competitor/analytics/comprehensive-discount-rate', { params })
  },

  /**
   * 获取总体促销强度指数
   * @param {object} params - 查询参数 (task_id, date_range)
   * @returns {Promise}
   */
  getOverallPromotionIntensity(params = {}) {
    return apiClient.get('/competitor/analytics/overall-promotion-intensity', { params })
  },

  /**
   * 获取单品价格综合偏差率
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getComprehensiveDeviationRate(params = {}) {
    return apiClient.get('/competitor/analytics/comprehensive-deviation-rate', { params })
  },

  /**
   * 获取单品最低价偏差率
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getMinPriceDeviationRate(params = {}) {
    return apiClient.get('/competitor/analytics/min-price-deviation-rate', { params })
  },

  /**
   * 获取单品最高价偏差率
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getMaxPriceDeviationRate(params = {}) {
    return apiClient.get('/competitor/analytics/max-price-deviation-rate', { params })
  },

  /**
   * 获取竞品价格趋势斜率
   * @param {object} params - 查询参数 (task_id, item_id, days)
   * @returns {Promise}
   */
  getPriceTrendSlope(params = {}) {
    return apiClient.get('/competitor/analytics/price-trend-slope', { params })
  },

  /**
   * 获取价格趋势图表数据
   * @param {object} params - 查询参数 (task_id, item_ids, period)
   * @returns {Promise}
   */
  getPriceTrendChart(params = {}) {
    return apiClient.get('/competitor/analytics/price-trend-chart', { params })
  },

  /**
   * 获取促销类型分布数据
   * @param {object} params - 查询参数 (task_id, date_range)
   * @returns {Promise}
   */
  getPromotionDistribution(params = {}) {
    return apiClient.get('/competitor/analytics/promotion-distribution', { params })
  },

  /**
   * 获取类目价格带分布热力图数据
   * @param {object} params - 查询参数 (task_id, category_id, date_range)
   * @returns {Promise}
   */
  getCategoryPriceHeatmap(params = {}) {
    return apiClient.get('/competitor/analytics/category-price-heatmap', { params })
  },

  /**
   * 批量计算竞品核心指标
   * @param {object} data - 计算参数 (task_ids, item_ids, force_recalculate)
   * @returns {Promise}
   */
  batchCalculateMetrics(data) {
    return apiClient.post('/competitor/analytics/batch-calculate-metrics', data)
  },

  /**
   * 获取竞品对比分析
   * @param {object} params - 查询参数 (task_id, item_ids, metrics)
   * @returns {Promise}
   */
  getCompetitorComparison(params = {}) {
    return apiClient.get('/competitor/analytics/competitor-comparison', { params })
  },

  /**
   * 获取竞品监测统计数据
   * @param {object} params - 查询参数 (date_range, task_id)
   * @returns {Promise}
   */
  getCompetitorStats(params = {}) {
    return apiClient.get('/competitor/analytics/stats', { params })
  },

  /**
   * 导出竞品分析报告
   * @param {object} params - 导出参数 (task_id, date_range, format)
   * @returns {Promise}
   */
  exportAnalysisReport(params = {}) {
    return apiClient.get('/competitor/analytics/export-report', { 
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取竞品价格预测
   * @param {object} params - 预测参数 (task_id, item_id, days)
   * @returns {Promise}
   */
  getPriceForecast(params = {}) {
    return apiClient.get('/competitor/analytics/price-forecast', { params })
  },

  /**
   * 获取竞品促销活动分析
   * @param {object} params - 查询参数 (task_id, date_range, promotion_type)
   * @returns {Promise}
   */
  getPromotionAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/promotion-analysis', { params })
  },

  /**
   * 获取竞品市场份额分析
   * @param {object} params - 查询参数 (task_id, category_id, date_range)
   * @returns {Promise}
   */
  getMarketShareAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/market-share-analysis', { params })
  },

  /**
   * 获取竞品价格敏感度分析
   * @param {object} params - 查询参数 (task_id, item_id, price_range)
   * @returns {Promise}
   */
  getPriceSensitivityAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/price-sensitivity-analysis', { params })
  },

  /**
   * 获取竞品库存变化趋势
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getStockTrend(params = {}) {
    return apiClient.get('/competitor/analytics/stock-trend', { params })
  },

  /**
   * 获取竞品销量趋势分析
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getSalesTrend(params = {}) {
    return apiClient.get('/competitor/analytics/sales-trend', { params })
  },

  /**
   * 获取竞品评价情感分析
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getReviewSentimentAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/review-sentiment-analysis', { params })
  },

  /**
   * 获取竞品新品上架监测
   * @param {object} params - 查询参数 (task_id, date_range, category_id)
   * @returns {Promise}
   */
  getNewProductMonitoring(params = {}) {
    return apiClient.get('/competitor/analytics/new-product-monitoring', { params })
  },

  /**
   * 获取竞品价格波动预警
   * @param {object} params - 查询参数 (task_id, threshold, date_range)
   * @returns {Promise}
   */
  getPriceFluctuationAlerts(params = {}) {
    return apiClient.get('/competitor/analytics/price-fluctuation-alerts', { params })
  },

  /**
   * 获取竞品渠道分析
   * @param {object} params - 查询参数 (task_id, channel_type, date_range)
   * @returns {Promise}
   */
  getChannelAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/channel-analysis', { params })
  },

  /**
   * 获取竞品季节性分析
   * @param {object} params - 查询参数 (task_id, item_id, year)
   * @returns {Promise}
   */
  getSeasonalityAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/seasonality-analysis', { params })
  },

  /**
   * 获取竞品关键词分析
   * @param {object} params - 查询参数 (task_id, keywords, date_range)
   * @returns {Promise}
   */
  getKeywordAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/keyword-analysis', { params })
  },

  /**
   * 获取竞品品牌对比分析
   * @param {object} params - 查询参数 (task_id, brands, metrics, date_range)
   * @returns {Promise}
   */
  getBrandComparison(params = {}) {
    return apiClient.get('/competitor/analytics/brand-comparison', { params })
  },

  /**
   * 获取竞品地域分析
   * @param {object} params - 查询参数 (task_id, regions, date_range)
   * @returns {Promise}
   */
  getRegionalAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/regional-analysis', { params })
  },

  /**
   * 获取竞品用户画像分析
   * @param {object} params - 查询参数 (task_id, item_id, date_range)
   * @returns {Promise}
   */
  getUserProfileAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/user-profile-analysis', { params })
  },

  /**
   * 获取竞品营销活动效果分析
   * @param {object} params - 查询参数 (task_id, campaign_id, date_range)
   * @returns {Promise}
   */
  getCampaignEffectivenessAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/campaign-effectiveness-analysis', { params })
  },

  /**
   * 获取竞品供应链分析
   * @param {object} params - 查询参数 (task_id, supplier_info, date_range)
   * @returns {Promise}
   */
  getSupplyChainAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/supply-chain-analysis', { params })
  },

  /**
   * 获取竞品技术创新分析
   * @param {object} params - 查询参数 (task_id, innovation_keywords, date_range)
   * @returns {Promise}
   */
  getInnovationAnalysis(params = {}) {
    return apiClient.get('/competitor/analytics/innovation-analysis', { params })
  },

  /**
   * 获取竞品风险评估
   * @param {object} params - 查询参数 (task_id, risk_factors, date_range)
   * @returns {Promise}
   */
  getRiskAssessment(params = {}) {
    return apiClient.get('/competitor/analytics/risk-assessment', { params })
  },

  /**
   * 获取竞品机会识别
   * @param {object} params - 查询参数 (task_id, opportunity_criteria, date_range)
   * @returns {Promise}
   */
  getOpportunityIdentification(params = {}) {
    return apiClient.get('/competitor/analytics/opportunity-identification', { params })
  }
}

export default competitorAnalyticsApi
