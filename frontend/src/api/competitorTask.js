import apiClient from './apiClient'

/**
 * 竞品任务API服务
 */
const competitorTaskApi = {
  /**
   * 获取竞品监测任务列表
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTasks(params = {}) {
    return apiClient.get('/competitor-monitoring-tasks', { params })
  },

  /**
   * 获取单个竞品监测任务
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTask(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}`)
  },

  /**
   * 创建竞品监测任务
   * @param {object} data - 任务数据
   * @returns {Promise}
   */
  createTask(data) {
    return apiClient.post('/competitor-monitoring-tasks', data)
  },

  /**
   * 更新竞品监测任务
   * @param {number} id - 任务ID
   * @param {object} data - 任务数据
   * @returns {Promise}
   */
  updateTask(id, data) {
    return apiClient.put(`/competitor-monitoring-tasks/${id}`, data)
  },

  /**
   * 删除竞品监测任务
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  deleteTask(id) {
    return apiClient.delete(`/competitor-monitoring-tasks/${id}`)
  },

  /**
   * 启动竞品监测任务
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  startTask(id) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/start`)
  },

  /**
   * 暂停竞品监测任务
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  pauseTask(id) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/pause`)
  },

  /**
   * 立即执行竞品监测任务
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  runTaskNow(id) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/run-now`)
  },

  /**
   * 获取任务统计信息
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTaskStats(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/stats`)
  },

  /**
   * 获取任务数据
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskData(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/data`, { params })
  },

  /**
   * 获取任务看板数据
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskDashboard(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/dashboard`, { params })
  },

  /**
   * 批量操作任务
   * @param {object} data - 批量操作数据
   * @returns {Promise}
   */
  batchOperation(data) {
    return apiClient.post('/competitor-monitoring-tasks/batch', data)
  },

  /**
   * 导出任务数据
   * @param {number} id - 任务ID
   * @param {object} params - 导出参数
   * @returns {Promise}
   */
  exportTaskData(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/export`, {
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取任务执行日志
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskLogs(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/logs`, { params })
  },

  /**
   * 获取任务配置模板
   * @returns {Promise}
   */
  getTaskTemplate() {
    return apiClient.get('/competitor-monitoring-tasks/template')
  },

  /**
   * 复制任务
   * @param {number} id - 任务ID
   * @param {object} data - 复制配置
   * @returns {Promise}
   */
  duplicateTask(id, data = {}) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/duplicate`, data)
  },

  /**
   * 获取任务状态选项
   * @returns {Promise}
   */
  getTaskStatuses() {
    return apiClient.get('/competitor-monitoring-tasks/statuses')
  },

  /**
   * 获取任务频率选项
   * @returns {Promise}
   */
  getTaskFrequencies() {
    return apiClient.get('/competitor-monitoring-tasks/frequencies')
  },

  /**
   * 验证任务配置
   * @param {object} data - 任务配置
   * @returns {Promise}
   */
  validateTaskConfig(data) {
    return apiClient.post('/competitor-monitoring-tasks/validate', data)
  },

  /**
   * 获取任务性能指标
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskPerformance(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/performance`, { params })
  },

  /**
   * 获取任务预警规则
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTaskAlertRules(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/alert-rules`)
  },

  /**
   * 设置任务预警规则
   * @param {number} id - 任务ID
   * @param {object} data - 预警规则数据
   * @returns {Promise}
   */
  setTaskAlertRules(id, data) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/alert-rules`, data)
  },

  /**
   * 获取任务数据源配置
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTaskDataSources(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/data-sources`)
  },

  /**
   * 更新任务数据源配置
   * @param {number} id - 任务ID
   * @param {object} data - 数据源配置
   * @returns {Promise}
   */
  updateTaskDataSources(id, data) {
    return apiClient.put(`/competitor-monitoring-tasks/${id}/data-sources`, data)
  },

  /**
   * 测试任务数据源连接
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  testTaskDataSources(id) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/test-connection`)
  },

  /**
   * 获取任务调度信息
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTaskSchedule(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/schedule`)
  },

  /**
   * 更新任务调度信息
   * @param {number} id - 任务ID
   * @param {object} data - 调度配置
   * @returns {Promise}
   */
  updateTaskSchedule(id, data) {
    return apiClient.put(`/competitor-monitoring-tasks/${id}/schedule`, data)
  },

  /**
   * 获取任务历史记录
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskHistory(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/history`, { params })
  },

  /**
   * 获取任务错误日志
   * @param {number} id - 任务ID
   * @param {object} params - 查询参数
   * @returns {Promise}
   */
  getTaskErrors(id, params = {}) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/errors`, { params })
  },

  /**
   * 清理任务数据
   * @param {number} id - 任务ID
   * @param {object} data - 清理配置
   * @returns {Promise}
   */
  cleanupTaskData(id, data) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/cleanup`, data)
  },

  /**
   * 获取任务依赖关系
   * @param {number} id - 任务ID
   * @returns {Promise}
   */
  getTaskDependencies(id) {
    return apiClient.get(`/competitor-monitoring-tasks/${id}/dependencies`)
  },

  /**
   * 设置任务依赖关系
   * @param {number} id - 任务ID
   * @param {object} data - 依赖配置
   * @returns {Promise}
   */
  setTaskDependencies(id, data) {
    return apiClient.post(`/competitor-monitoring-tasks/${id}/dependencies`, data)
  }
}

export default competitorTaskApi
