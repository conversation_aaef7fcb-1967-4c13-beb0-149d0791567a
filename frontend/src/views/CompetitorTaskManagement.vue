<template>
  <div class="competitor-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">竞品动态监测</h1>
        <p class="page-description">管理竞品监控任务，分析竞争对手价格策略和促销活动</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="default" @click="showCreateTaskDialog" :loading="loading">
          <el-icon><Plus /></el-icon>
          <span class="btn-text">新增任务</span>
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon primary">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.taskGroups || 0 }}</div>
          <div class="stats-label">任务组</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon success">
          <el-icon><ShoppingBag /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.competitorProducts || 0 }}</div>
          <div class="stats-label">竞品数量</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon warning">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.runningTasks || 0 }}</div>
          <div class="stats-label">运行中</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon danger">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.alerts || 0 }}</div>
          <div class="stats-label">预警</div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h2 class="section-title">任务列表</h2>
        <div class="section-actions">
          <el-input 
            v-model="searchKeyword"
            placeholder="搜索任务..." 
            class="search-input"
            prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
          <el-button @click="refreshTasks" :loading="tasksLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="task-cards" v-loading="tasksLoading">
        <div 
          class="task-card" 
          v-for="task in filteredTasks" 
          :key="task.id"
          @click="viewTaskDetails(task)"
        >
          <div class="task-header">
            <div :class="['task-status', task.status]"></div>
            <h3 class="task-name">{{ task.name }}</h3>
            <el-dropdown @command="(cmd) => handleTaskAction(cmd, task)" @click.stop="">
              <el-button size="small" link>
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                  <el-dropdown-item command="run-now">立即执行</el-dropdown-item>
                  <el-dropdown-item command="start" v-if="task.status !== 'running'">启动任务</el-dropdown-item>
                  <el-dropdown-item command="pause" v-if="task.status === 'running'">暂停任务</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="task-info">
            <div class="task-description">{{ task.description || '暂无描述' }}</div>
            <div class="task-meta">
              <span class="meta-item">
                <el-icon><FolderOpened /></el-icon>
                {{ task.task_group?.name || '未分组' }}
              </span>
              <span class="meta-item">
                <el-icon><DataBoard /></el-icon>
                {{ task.data_source?.name || '未配置' }}
              </span>
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatFrequency(task.frequency_type, task.frequency_value) }}
              </span>
            </div>
          </div>
          <div class="task-stats">
            <div class="stat-item">
              <div class="stat-value">{{ task.target_products?.length || 0 }}</div>
              <div class="stat-label">目标商品</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.competitor_keywords?.length || 0 }}</div>
              <div class="stat-label">关键词</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.our_guide_price_avg || '-' }}</div>
              <div class="stat-label">我方指导价</div>
            </div>
          </div>
          <div class="task-footer">
            <div class="task-time">
              <span>最后执行：{{ formatDateTime(task.last_run_at) || '未执行' }}</span>
              <span>下次执行：{{ formatDateTime(task.next_run_at) || '未安排' }}</span>
            </div>
          </div>
        </div>
        
        <div class="empty-state" v-if="!tasksLoading && filteredTasks.length === 0">
          <el-empty description="暂无竞品监控任务">
            <el-button type="primary" @click="showCreateTaskDialog">创建第一个任务</el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  FolderOpened,
  ShoppingBag,
  Timer,
  Warning,
  Setting,
  DataBoard,
  Clock
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tasksLoading = ref(false)
const searchKeyword = ref('')

// 统计数据
const statistics = ref({
  taskGroups: 0,
  competitorProducts: 0,
  runningTasks: 0,
  alerts: 0
})

// 任务列表
const tasks = ref([])
const taskGroups = ref([])
const dataSources = ref([])

// 计算属性
const filteredTasks = computed(() => {
  if (!searchKeyword.value) return tasks.value
  return tasks.value.filter(task => 
    task.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    task.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadStatistics = async () => {
  try {
    // TODO: 调用API获取统计数据
    statistics.value = {
      taskGroups: 5,
      competitorProducts: 128,
      runningTasks: 3,
      alerts: 12
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadTasks = async () => {
  tasksLoading.value = true
  try {
    // TODO: 调用API获取任务列表
    // const response = await competitorTaskApi.getTasks()
    
    // 模拟数据
    tasks.value = [
      {
        id: 1,
        name: '手机竞品价格监控',
        description: '监控主流手机品牌的价格变化和促销策略',
        status: 'running',
        task_group: { name: '3C数码组' },
        data_source: { name: '综合电商API' },
        frequency_type: 'interval',
        frequency_value: 360,
        target_products: ['12345', '67890'],
        competitor_keywords: ['iPhone', '华为', '小米'],
        our_guide_price_avg: 3999,
        last_run_at: '2024-01-15 14:30:00',
        next_run_at: '2024-01-15 20:30:00'
      }
    ]
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    tasksLoading.value = false
  }
}

const refreshTasks = () => {
  loadTasks()
  loadStatistics()
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const showCreateTaskDialog = () => {
  ElMessage.info('创建任务功能开发中...')
}

const viewTaskDetails = (task) => {
  ElMessage.info(`查看任务详情: ${task.name}`)
}

const handleTaskAction = (command, task) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑任务: ${task.name}`)
      break
    case 'run-now':
      ElMessage.info(`立即执行任务: ${task.name}`)
      break
    case 'start':
      ElMessage.info(`启动任务: ${task.name}`)
      break
    case 'pause':
      ElMessage.info(`暂停任务: ${task.name}`)
      break
    case 'delete':
      ElMessage.info(`删除任务: ${task.name}`)
      break
  }
}

const formatFrequency = (type, value) => {
  if (type === 'interval') {
    return `每${value}分钟`
  } else if (type === 'cron') {
    return '定时执行'
  }
  return '未设置'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return null
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadTasks()
  loadStatistics()
})
</script>

<style scoped>
/* 基础布局 */
.competitor-task-management {
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stats-icon.primary {
  background-color: #e3f2fd;
  color: #1976d2;
}

.stats-icon.success {
  background-color: #e8f5e8;
  color: #388e3c;
}

.stats-icon.warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.stats-icon.danger {
  background-color: #ffebee;
  color: #d32f2f;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #64748b;
}

/* 任务列表 */
.task-list-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 280px;
}

.task-cards {
  padding: 24px;
}

.task-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.task-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 12px;
}

.task-status.running {
  background-color: #67c23a;
}

.task-status.paused {
  background-color: #e6a23c;
}

.task-status.stopped {
  background-color: #f56c6c;
}

.task-name {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.task-info {
  margin-bottom: 16px;
}

.task-description {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 8px;
}

.task-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.task-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.task-footer {
  border-top: 1px solid #e9ecef;
  padding-top: 12px;
}

.task-time {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #64748b;
}

.empty-state {
  text-align: center;
  padding: 40px;
}
</style>
