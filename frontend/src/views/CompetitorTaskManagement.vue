<template>
  <div class="competitor-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">竞品动态监测</h1>
        <p class="page-description">管理竞品监控任务，分析竞争对手价格策略和促销活动</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="default" @click="showCreateTaskDialog" :loading="loading">
          <el-icon><Plus /></el-icon>
          <span class="btn-text">新增任务</span>
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon primary">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.taskGroups || 0 }}</div>
          <div class="stats-label">任务组</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon success">
          <el-icon><ShoppingBag /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.competitorProducts || 0 }}</div>
          <div class="stats-label">竞品数量</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon warning">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.runningTasks || 0 }}</div>
          <div class="stats-label">运行中</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon danger">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.alerts || 0 }}</div>
          <div class="stats-label">预警</div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h2 class="section-title">任务列表</h2>
        <div class="section-actions">
          <el-input 
            v-model="searchKeyword"
            placeholder="搜索任务..." 
            class="search-input"
            prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
          <el-button @click="refreshTasks" :loading="tasksLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="task-cards" v-loading="tasksLoading">
        <div 
          class="task-card" 
          v-for="task in filteredTasks" 
          :key="task.id"
          @click="viewTaskDetails(task)"
        >
          <div class="task-header">
            <div :class="['task-status', task.status]"></div>
            <h3 class="task-name">{{ task.name }}</h3>
            <el-dropdown @command="(cmd) => handleTaskAction(cmd, task)" @click.stop="">
              <el-button size="small" link>
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                  <el-dropdown-item command="run-now">立即执行</el-dropdown-item>
                  <el-dropdown-item command="start" v-if="task.status !== 'running'">启动任务</el-dropdown-item>
                  <el-dropdown-item command="pause" v-if="task.status === 'running'">暂停任务</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="task-info">
            <div class="task-description">{{ task.description || '暂无描述' }}</div>
            <div class="task-meta">
              <span class="meta-item">
                <el-icon><FolderOpened /></el-icon>
                {{ task.task_group?.name || '未分组' }}
              </span>
              <span class="meta-item">
                <el-icon><DataBoard /></el-icon>
                {{ task.data_source?.name || '未配置' }}
              </span>
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatFrequency(task.frequency_type, task.frequency_value) }}
              </span>
            </div>
          </div>
          <div class="task-stats">
            <div class="stat-item">
              <div class="stat-value">{{ task.target_products?.length || 0 }}</div>
              <div class="stat-label">目标商品</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.competitor_keywords?.length || 0 }}</div>
              <div class="stat-label">关键词</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ task.our_guide_price_avg || '-' }}</div>
              <div class="stat-label">我方指导价</div>
            </div>
          </div>
          <div class="task-footer">
            <div class="task-time">
              <span>最后执行：{{ formatDateTime(task.last_run_at) || '未执行' }}</span>
              <span>下次执行：{{ formatDateTime(task.next_run_at) || '未安排' }}</span>
            </div>
          </div>
        </div>
        
        <div class="empty-state" v-if="!tasksLoading && filteredTasks.length === 0">
          <el-empty description="暂无竞品监控任务">
            <el-button type="primary" @click="showCreateTaskDialog">创建第一个任务</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 任务创建/编辑对话框 -->
    <el-dialog
      v-model="taskDialog.visible"
      :title="taskDialog.title"
      width="800px"
      :close-on-click-modal="false"
      @close="resetTaskForm"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskFormRules"
        label-width="120px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input
                v-model="taskForm.name"
                placeholder="请输入任务名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务组" prop="task_group_id">
              <el-select
                v-model="taskForm.task_group_id"
                placeholder="请选择任务组"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="group in taskGroups"
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据源" prop="data_source_id">
              <el-select
                v-model="taskForm.data_source_id"
                placeholder="请选择数据源"
                style="width: 100%"
              >
                <el-option
                  v-for="source in dataSources"
                  :key="source.id"
                  :label="source.name"
                  :value="source.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集频率" prop="frequency_type">
              <el-radio-group v-model="taskForm.frequency_type">
                <el-radio value="interval">按间隔</el-radio>
                <el-radio value="cron">按计划</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="taskForm.frequency_type === 'interval'">
          <el-col :span="12">
            <el-form-item label="间隔时间(分钟)" prop="frequency_value">
              <el-input-number
                v-model="taskForm.frequency_value"
                :min="1"
                :max="10080"
                placeholder="请输入间隔时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="taskForm.frequency_type === 'cron'">
          <el-col :span="12">
            <el-form-item label="Cron表达式" prop="cron_expression">
              <el-input
                v-model="taskForm.cron_expression"
                placeholder="如: 0 */6 * * *"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="监控商品ID" prop="target_products">
          <el-input
            v-model="targetProductsInput"
            type="textarea"
            :rows="3"
            placeholder="请输入商品ID，每行一个或用逗号分隔"
            @blur="parseTargetProducts"
          />
          <div class="form-tip">
            支持多种格式：每行一个ID，或用逗号、分号、空格分隔
          </div>
        </el-form-item>

        <el-form-item label="竞品关键词">
          <el-input
            v-model="competitorKeywordsInput"
            type="textarea"
            :rows="2"
            placeholder="请输入竞品关键词，用逗号分隔"
            @blur="parseCompetitorKeywords"
          />
          <div class="form-tip">
            用于识别竞品商品，如：iPhone,华为,小米
          </div>
        </el-form-item>

        <el-form-item label="监控字段" prop="monitor_fields">
          <el-checkbox-group v-model="taskForm.monitor_fields">
            <el-checkbox
              v-for="field in monitorFieldOptions"
              :key="field.value"
              :value="field.value"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="我方指导价(最低)">
              <el-input-number
                v-model="taskForm.our_guide_price_min"
                :min="0"
                :precision="2"
                placeholder="最低价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="我方指导价(最高)">
              <el-input-number
                v-model="taskForm.our_guide_price_max"
                :min="0"
                :precision="2"
                placeholder="最高价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="我方指导价(平均)">
              <el-input-number
                v-model="taskForm.our_guide_price_avg"
                :min="0"
                :precision="2"
                placeholder="平均价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="预警规则">
          <el-select
            v-model="taskForm.alert_rule_ids"
            multiple
            placeholder="请选择预警规则"
            style="width: 100%"
          >
            <el-option
              v-for="rule in alertRules"
              :key="rule.id"
              :label="rule.name"
              :value="rule.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="taskDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveTask" :loading="loading">
            {{ taskDialog.isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  FolderOpened,
  ShoppingBag,
  Timer,
  Warning,
  Setting,
  DataBoard,
  Clock
} from '@element-plus/icons-vue'
import competitorTaskApi from '../api/competitorTask'

// 响应式数据
const loading = ref(false)
const tasksLoading = ref(false)
const searchKeyword = ref('')

// 统计数据
const statistics = ref({
  taskGroups: 0,
  competitorProducts: 0,
  runningTasks: 0,
  alerts: 0
})

// 任务列表
const tasks = ref([])
const taskGroups = ref([])
const dataSources = ref([])

// 任务对话框
const taskDialog = reactive({
  visible: false,
  isEdit: false,
  title: '新建竞品监控任务',
  editId: null
})

// 任务表单
const taskForm = reactive({
  name: '',
  description: '',
  task_group_id: '',
  data_source_id: '',
  target_products: [],
  competitor_keywords: [],
  monitor_fields: [],
  frequency_type: 'interval',
  frequency_value: 60,
  cron_expression: '',
  our_guide_price_min: '',
  our_guide_price_max: '',
  our_guide_price_avg: '',
  alert_rule_ids: []
})

// 表单引用
const taskFormRef = ref()

// 预警规则选项
const alertRules = ref([])

// 监控字段选项
const monitorFieldOptions = [
  { label: '商品名称', value: 'product_name' },
  { label: '价格', value: 'price' },
  { label: '到手价', value: 'sub_price' },
  { label: '促销信息', value: 'promotion' },
  { label: '库存状态', value: 'stock_status' },
  { label: '销量', value: 'sales_volume' },
  { label: '评价数', value: 'review_count' },
  { label: '店铺信息', value: 'shop_info' }
]

// 辅助输入变量
const targetProductsInput = ref('')
const competitorKeywordsInput = ref('')

// 表单验证规则
const taskFormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 100, message: '任务名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  data_source_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  target_products: [
    { required: true, message: '请输入监控商品ID', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请输入至少一个商品ID'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  monitor_fields: [
    { required: true, message: '请选择监控字段', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少选择一个监控字段'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  frequency_value: [
    { required: true, message: '请输入采集间隔时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '间隔时间应在1-10080分钟之间', trigger: 'blur' }
  ],
  cron_expression: [
    {
      validator: (rule, value, callback) => {
        if (taskForm.frequency_type === 'cron' && !value) {
          callback(new Error('请输入Cron表达式'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const filteredTasks = computed(() => {
  if (!searchKeyword.value) return tasks.value
  return tasks.value.filter(task =>
    task.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    task.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadStatistics = async () => {
  try {
    // TODO: 调用API获取统计数据
    statistics.value = {
      taskGroups: 5,
      competitorProducts: 128,
      runningTasks: 3,
      alerts: 12
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadTasks = async () => {
  tasksLoading.value = true
  try {
    const response = await competitorTaskApi.getTasks()
    console.log('API Response:', response.data) // 调试日志

    if (response.data.success) {
      // 处理分页数据
      if (response.data.data && response.data.data.data) {
        tasks.value = response.data.data.data || []
      } else {
        tasks.value = response.data.data || []
      }
    } else {
      tasks.value = []
      ElMessage.error(response.data.message || '加载任务列表失败')
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
    console.error('Error details:', error.response?.data) // 详细错误信息
    tasks.value = []

    // 显示具体错误信息
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else if (error.response?.status === 401) {
      ElMessage.error('请先登录')
    } else if (error.response?.status === 404) {
      ElMessage.error('API接口不存在')
    } else {
      ElMessage.error('加载任务列表失败')
    }

    // 如果是网络错误或API不存在，显示模拟数据
    if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
      tasks.value = [
        {
          id: 1,
          name: '手机竞品价格监控',
          description: '监控主流手机品牌的价格变化和促销策略',
          status: 'running',
          task_group: { name: '3C数码组' },
          data_source: { name: '综合电商API' },
          frequency_type: 'interval',
          frequency_value: 360,
          target_products: ['12345', '67890'],
          competitor_keywords: ['iPhone', '华为', '小米'],
          our_guide_price_avg: 3999,
          last_run_at: '2024-01-15 14:30:00',
          next_run_at: '2024-01-15 20:30:00'
        }
      ]
    } else {
      ElMessage.error('加载任务列表失败')
    }
  } finally {
    tasksLoading.value = false
  }
}

const refreshTasks = () => {
  loadTasks()
  loadStatistics()
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const showCreateTaskDialog = () => {
  taskDialog.visible = true
  taskDialog.isEdit = false
  taskDialog.title = '新建竞品监控任务'
  resetTaskForm()
  loadTaskGroups()
  loadDataSources()
  loadAlertRules()
}

// 重置表单
const resetTaskForm = () => {
  Object.assign(taskForm, {
    name: '',
    description: '',
    task_group_id: '',
    data_source_id: '',
    target_products: [],
    competitor_keywords: [],
    monitor_fields: [],
    frequency_type: 'interval',
    frequency_value: 60,
    cron_expression: '',
    our_guide_price_min: '',
    our_guide_price_max: '',
    our_guide_price_avg: '',
    alert_rule_ids: []
  })
  targetProductsInput.value = ''
  competitorKeywordsInput.value = ''
  taskFormRef.value?.clearValidate()
}

// 解析商品ID输入
const parseTargetProducts = () => {
  if (!targetProductsInput.value.trim()) {
    taskForm.target_products = []
    return
  }

  const products = targetProductsInput.value
    .split(/[,;\n\s]+/)
    .map(id => id.trim())
    .filter(id => id.length > 0)

  taskForm.target_products = [...new Set(products)] // 去重
}

// 解析竞品关键词输入
const parseCompetitorKeywords = () => {
  if (!competitorKeywordsInput.value.trim()) {
    taskForm.competitor_keywords = []
    return
  }

  const keywords = competitorKeywordsInput.value
    .split(/[,;\n]+/)
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)

  taskForm.competitor_keywords = [...new Set(keywords)] // 去重
}

// 保存任务
const saveTask = async () => {
  if (!taskFormRef.value) return

  try {
    const valid = await taskFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 确保解析了输入的商品ID和关键词
    parseTargetProducts()
    parseCompetitorKeywords()

    const taskData = { ...taskForm }

    if (taskDialog.isEdit) {
      // 调用更新API
      const response = await competitorTaskApi.updateTask(taskDialog.editId, taskData)
      if (response.data.success) {
        ElMessage.success('任务更新成功')
        taskDialog.visible = false
        refreshTasks()
      } else {
        ElMessage.error(response.data.message || '更新任务失败')
      }
    } else {
      // 调用创建API
      const response = await competitorTaskApi.createTask(taskData)
      if (response.data.success) {
        ElMessage.success('任务创建成功')
        taskDialog.visible = false
        refreshTasks()
      } else {
        ElMessage.error(response.data.message || '创建任务失败')
      }
    }
  } catch (error) {
    console.error('保存任务失败:', error)
    ElMessage.error('保存任务失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 加载任务组列表
const loadTaskGroups = async () => {
  try {
    // TODO: 调用API获取任务组列表
    taskGroups.value = [
      { id: 1, name: '3C数码组' },
      { id: 2, name: '家电组' },
      { id: 3, name: '服装组' }
    ]
  } catch (error) {
    console.error('加载任务组失败:', error)
  }
}

// 加载数据源列表
const loadDataSources = async () => {
  try {
    // TODO: 调用API获取数据源列表
    dataSources.value = [
      { id: 1, name: '综合电商API' },
      { id: 2, name: '京东API' },
      { id: 3, name: '淘宝API' }
    ]
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

// 加载预警规则列表
const loadAlertRules = async () => {
  try {
    // TODO: 调用API获取预警规则列表
    alertRules.value = [
      { id: 1, name: '价格偏差率预警' },
      { id: 2, name: '促销强度预警' },
      { id: 3, name: '库存变化预警' }
    ]
  } catch (error) {
    console.error('加载预警规则失败:', error)
  }
}

const viewTaskDetails = (task) => {
  ElMessage.info(`查看任务详情: ${task.name}`)
}

const handleTaskAction = async (command, task) => {
  switch (command) {
    case 'edit':
      await editTask(task)
      break
    case 'run-now':
      await runTaskNow(task)
      break
    case 'start':
      await startTask(task)
      break
    case 'pause':
      await pauseTask(task)
      break
    case 'delete':
      await deleteTask(task)
      break
  }
}

// 编辑任务
const editTask = async (task) => {
  try {
    // 加载任务详情
    const response = await competitorTaskApi.getTask(task.id)
    if (response.data.success) {
      const taskData = response.data.data

      // 填充表单数据
      Object.assign(taskForm, {
        name: taskData.name || '',
        description: taskData.description || '',
        task_group_id: taskData.task_group_id || '',
        data_source_id: taskData.data_source_id || '',
        target_products: taskData.target_products || [],
        competitor_keywords: taskData.competitor_keywords || [],
        monitor_fields: taskData.monitor_fields || [],
        frequency_type: taskData.frequency_type || 'interval',
        frequency_value: taskData.frequency_value || 60,
        cron_expression: taskData.cron_expression || '',
        our_guide_price_min: taskData.our_guide_price_min || '',
        our_guide_price_max: taskData.our_guide_price_max || '',
        our_guide_price_avg: taskData.our_guide_price_avg || '',
        alert_rule_ids: taskData.alert_rule_ids || []
      })

      // 填充辅助输入框
      targetProductsInput.value = (taskData.target_products || []).join('\n')
      competitorKeywordsInput.value = (taskData.competitor_keywords || []).join(', ')

      // 显示编辑对话框
      taskDialog.visible = true
      taskDialog.isEdit = true
      taskDialog.title = '编辑竞品监控任务'
      taskDialog.editId = task.id

      // 加载选项数据
      loadTaskGroups()
      loadDataSources()
      loadAlertRules()
    }
  } catch (error) {
    console.error('加载任务详情失败:', error)
    ElMessage.error('加载任务详情失败')
  }
}

// 立即执行任务
const runTaskNow = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要立即执行任务"${task.name}"吗？`,
      '确认执行',
      { type: 'warning' }
    )

    loading.value = true
    const response = await competitorTaskApi.runTaskNow(task.id)

    if (response.data.success) {
      ElMessage.success('任务已提交执行')
      refreshTasks()
    } else {
      ElMessage.error(response.data.message || '执行任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行任务失败:', error)
      ElMessage.error('执行任务失败')
    }
  } finally {
    loading.value = false
  }
}

// 启动任务
const startTask = async (task) => {
  try {
    loading.value = true
    const response = await competitorTaskApi.startTask(task.id)

    if (response.data.success) {
      ElMessage.success('任务已启动')
      refreshTasks()
    } else {
      ElMessage.error(response.data.message || '启动任务失败')
    }
  } catch (error) {
    console.error('启动任务失败:', error)
    ElMessage.error('启动任务失败')
  } finally {
    loading.value = false
  }
}

// 暂停任务
const pauseTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要暂停任务"${task.name}"吗？`,
      '确认暂停',
      { type: 'warning' }
    )

    loading.value = true
    const response = await competitorTaskApi.pauseTask(task.id)

    if (response.data.success) {
      ElMessage.success('任务已暂停')
      refreshTasks()
    } else {
      ElMessage.error(response.data.message || '暂停任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停任务失败:', error)
      ElMessage.error('暂停任务失败')
    }
  } finally {
    loading.value = false
  }
}

// 删除任务
const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${task.name}"吗？删除后无法恢复。`,
      '确认删除',
      {
        type: 'error',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    loading.value = true
    const response = await competitorTaskApi.deleteTask(task.id)

    if (response.data.success) {
      ElMessage.success('任务已删除')
      refreshTasks()
    } else {
      ElMessage.error(response.data.message || '删除任务失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      ElMessage.error('删除任务失败')
    }
  } finally {
    loading.value = false
  }
}

const formatFrequency = (type, value) => {
  if (type === 'interval') {
    return `每${value}分钟`
  } else if (type === 'cron') {
    return '定时执行'
  }
  return '未设置'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return null
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadTasks()
  loadStatistics()
})
</script>

<style scoped>
/* 基础布局 */
.competitor-task-management {
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stats-icon.primary {
  background-color: #e3f2fd;
  color: #1976d2;
}

.stats-icon.success {
  background-color: #e8f5e8;
  color: #388e3c;
}

.stats-icon.warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.stats-icon.danger {
  background-color: #ffebee;
  color: #d32f2f;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #64748b;
}

/* 任务列表 */
.task-list-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 280px;
}

.task-cards {
  padding: 24px;
}

.task-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.task-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 12px;
}

.task-status.running {
  background-color: #67c23a;
}

.task-status.paused {
  background-color: #e6a23c;
}

.task-status.stopped {
  background-color: #f56c6c;
}

.task-name {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.task-info {
  margin-bottom: 16px;
}

.task-description {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 8px;
}

.task-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.task-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.task-footer {
  border-top: 1px solid #e9ecef;
  padding-top: 12px;
}

.task-time {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #64748b;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 表单布局优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.el-checkbox {
  margin-right: 0;
}

/* 输入框样式优化 */
.el-input-number {
  width: 100%;
}

.el-textarea .el-textarea__inner {
  resize: vertical;
}
</style>
