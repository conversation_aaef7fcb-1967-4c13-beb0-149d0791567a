<template>
  <div class="competitor-dashboard">
    <!-- 页面标题和统计信息 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">竞品动态监测看板</h1>
        <p class="page-description">分析竞争对手价格策略、促销活动和市场动态</p>
      </div>
      <div class="header-stats">
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.taskGroups || 0 }}</div>
          <div class="stat-label">任务组</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.competitorProducts || 0 }}</div>
          <div class="stat-label">竞品数量</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.runningTasks || 0 }}</div>
          <div class="stat-label">运行中任务</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.alerts || 0 }}</div>
          <div class="stat-label">预警数量</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <el-row :gutter="16">
        <el-col :span="5">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索竞品名称或关键词"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.task_id"
            placeholder="选择监控任务"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="task in competitorTasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.data_source_id"
            placeholder="选择数据源"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 核心指标组件 -->
    <CompetitorMetrics />

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 价格趋势折线图 -->
        <el-col :span="12">
          <div class="chart-card" v-loading="chartsLoading.priceTrend">
            <div class="chart-header">
              <h3 class="chart-title">竞品价格趋势</h3>
              <div class="chart-actions">
                <el-select v-model="priceTrendPeriod" size="small" @change="loadPriceTrendChart">
                  <el-option label="近7天" value="7d" />
                  <el-option label="近30天" value="30d" />
                  <el-option label="近90天" value="90d" />
                </el-select>
              </div>
            </div>
            <div class="chart-content">
              <div ref="priceTrendChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>
        
        <!-- 促销类型占比饼图 -->
        <el-col :span="12">
          <div class="chart-card" v-loading="chartsLoading.promotionPie">
            <div class="chart-header">
              <h3 class="chart-title">促销类型分布</h3>
            </div>
            <div class="chart-content">
              <div ref="promotionPieChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 类目价格带分布热力图 -->
        <el-col :span="24">
          <div class="chart-card" v-loading="chartsLoading.categoryHeatmap">
            <div class="chart-header">
              <h3 class="chart-title">类目价格带分布热力图</h3>
              <div class="chart-actions">
                <el-select v-model="selectedCategory" size="small" @change="loadCategoryHeatmap" placeholder="选择类目">
                  <el-option
                    v-for="category in categories"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
              </div>
            </div>
            <div class="chart-content">
              <div ref="categoryHeatmapChart" class="chart-container-large"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 竞品数据表格 -->
    <div class="data-table-section">
      <div class="table-header">
        <h3 class="table-title">竞品数据详情</h3>
        <div class="table-actions">
          <el-button size="small" @click="exportData" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="competitorData"
        v-loading="tableLoading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="product_name" label="竞品名称" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="product-info">
              <div class="product-name">{{ row.product_name }}</div>
              <div class="product-meta">
                <span class="meta-item">ID: {{ row.item_id }}</span>
                <el-tag v-if="row.competitor_task" size="small" type="success">
                  {{ row.competitor_task.name }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="our_guide_price" label="我方指导价" width="120" sortable>
          <template #default="{ row }">
            <span class="price-text">¥{{ formatPrice(row.our_guide_price) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="competitor_price" label="竞品价格" width="120" sortable>
          <template #default="{ row }">
            <span class="price-text">¥{{ formatPrice(row.competitor_price) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="promotion_strategy_tendency" label="促销策略倾向" width="140">
          <template #default="{ row }">
            <el-tag :type="getPromotionTendencyType(row.promotion_strategy_tendency)" size="small">
              {{ getPromotionTendencyLabel(row.promotion_strategy_tendency) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="comprehensive_discount_rate" label="综合折扣率" width="120" sortable>
          <template #default="{ row }">
            <span :class="getDiscountRateClass(row.comprehensive_discount_rate)">
              {{ row.comprehensive_discount_rate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="price_deviation_rate" label="价格偏差率" width="120" sortable>
          <template #default="{ row }">
            <span :class="getDeviationRateClass(row.price_deviation_rate)">
              {{ row.price_deviation_rate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="price_trend_slope" label="价格趋势" width="100">
          <template #default="{ row }">
            <div class="trend-indicator">
              <el-icon v-if="row.price_trend_slope > 0" class="trend-up"><CaretTop /></el-icon>
              <el-icon v-else-if="row.price_trend_slope < 0" class="trend-down"><CaretBottom /></el-icon>
              <el-icon v-else class="trend-stable"><Minus /></el-icon>
              <span class="trend-value">{{ Math.abs(row.price_trend_slope || 0).toFixed(2) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="last_collected_at" label="最后更新" width="150" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.last_collected_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button text size="small" type="primary" @click="viewCompetitorDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  TrendCharts,
  PriceTag,
  DataAnalysis,
  Warning,
  CaretTop,
  CaretBottom,
  Minus
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import CompetitorMetrics from '@/components/CompetitorMetrics.vue'
import competitorAnalyticsApi from '@/api/competitorAnalytics'
import competitorTaskApi from '@/api/competitorTask'

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const metricsLoading = ref(true)
const tableLoading = ref(false)
const exportLoading = ref(false)

// 图表加载状态
const chartsLoading = reactive({
  priceTrend: false,
  promotionPie: false,
  categoryHeatmap: false
})

// 统计数据
const dashboardStats = ref({
  taskGroups: 0,
  competitorProducts: 0,
  runningTasks: 0,
  alerts: 0
})

// 核心指标
const coreMetrics = ref({
  promotionTendency: null,
  promotionTendencyChange: 0,
  avgDiscountRate: null,
  avgDiscountRateChange: 0,
  promotionIntensity: null,
  promotionIntensityChange: 0,
  avgDeviationRate: null,
  avgDeviationRateChange: 0
})

// 搜索表单
const searchForm = reactive({
  search: '',
  task_id: null,
  data_source_id: null,
  sort_by: 'last_collected_at',
  sort_order: 'desc'
})

const dateRange = ref([])

// 数据
const competitorData = ref([])
const competitorTasks = ref([])
const dataSources = ref([])
const categories = ref([])
const selectedProducts = ref([])

// 分页信息
const pagination = ref({
  current_page: 1,
  per_page: 10,
  total: 0
})

// 图表相关
const priceTrendChart = ref(null)
const promotionPieChart = ref(null)
const categoryHeatmapChart = ref(null)
const priceTrendPeriod = ref('30d')
const selectedCategory = ref(null)

// 图表实例
let priceTrendChartInstance = null
let promotionPieChartInstance = null
let categoryHeatmapChartInstance = null

// 方法
const loadStatistics = async () => {
  try {
    const response = await competitorAnalyticsApi.getCompetitorStats({
      date_range: dateRange.value,
      task_id: filters.task_id
    })

    if (response.data.success) {
      dashboardStats.value = response.data.data
    } else {
      // 使用模拟数据作为后备
      dashboardStats.value = {
        taskGroups: 8,
        competitorProducts: 256,
        runningTasks: 5,
        alerts: 18
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据作为后备
    dashboardStats.value = {
      taskGroups: 8,
      competitorProducts: 256,
      runningTasks: 5,
      alerts: 18
    }
  } finally {
    statsLoading.value = false
  }
}

const loadCoreMetrics = async () => {
  try {
    const response = await competitorAnalyticsApi.getCoreMetrics({
      task_id: filters.task_id,
      date_range: dateRange.value,
      category_id: filters.category_id
    })

    if (response.data.success) {
      coreMetrics.value = response.data.data
    } else {
      // 使用模拟数据作为后备
      coreMetrics.value = {
        promotionTendency: '积极',
        promotionTendencyChange: 5.2,
        avgDiscountRate: 15.8,
        avgDiscountRateChange: -2.1,
        promotionIntensity: 72.5,
        promotionIntensityChange: 8.3,
        avgDeviationRate: 12.4,
        avgDeviationRateChange: -1.5
      }
    }
  } catch (error) {
    console.error('加载核心指标失败:', error)
    // 使用模拟数据作为后备
    coreMetrics.value = {
      promotionTendency: '积极',
      promotionTendencyChange: 5.2,
      avgDiscountRate: 15.8,
      avgDiscountRateChange: -2.1,
      promotionIntensity: 72.5,
      promotionIntensityChange: 8.3,
      avgDeviationRate: 12.4,
      avgDeviationRateChange: -1.5
    }
  } finally {
    metricsLoading.value = false
  }
}

const loadCompetitorData = async () => {
  tableLoading.value = true
  try {
    const response = await competitorAnalyticsApi.getCompetitorComparison({
      task_id: filters.task_id,
      data_source_id: filters.data_source_id,
      category_id: filters.category_id,
      date_range: dateRange.value,
      page: pagination.value.current_page,
      per_page: pagination.value.per_page,
      sort_by: filters.sort_by,
      sort_order: filters.sort_order
    })

    if (response.data.success) {
      competitorData.value = response.data.data.data || []
      pagination.value = {
        current_page: response.data.data.current_page || 1,
        per_page: response.data.data.per_page || 10,
        total: response.data.data.total || 0
      }
    } else {
      // 使用模拟数据作为后备
      competitorData.value = [
        {
          id: 1,
          product_name: 'iPhone 15 Pro Max',
          item_id: 'COMP001',
          our_guide_price: 9999,
          competitor_price: 9299,
          promotion_strategy_tendency: 'active',
          comprehensive_discount_rate: 7.0,
          price_deviation_rate: -7.0,
          price_trend_slope: -0.5,
          last_collected_at: '2024-01-15 14:30:00',
          competitor_task: { name: '手机竞品监控' }
        },
        {
          id: 2,
          product_name: '华为Mate60 Pro',
          item_id: 'COMP002',
          our_guide_price: 6999,
          competitor_price: 6599,
          promotion_strategy_tendency: 'moderate',
          comprehensive_discount_rate: 5.7,
          price_deviation_rate: -5.7,
          price_trend_slope: 0.2,
          last_collected_at: '2024-01-15 14:25:00',
          competitor_task: { name: '手机竞品监控' }
        }
      ]
      pagination.value.total = 2
    }
  } catch (error) {
    console.error('加载竞品数据失败:', error)
    // 使用模拟数据作为后备
    competitorData.value = [
      {
        id: 1,
        product_name: 'iPhone 15 Pro Max',
        item_id: 'COMP001',
        our_guide_price: 9999,
        competitor_price: 9299,
        promotion_strategy_tendency: 'active',
        comprehensive_discount_rate: 7.0,
        price_deviation_rate: -7.0,
        price_trend_slope: -0.5,
        last_collected_at: '2024-01-15 14:30:00',
        competitor_task: { name: '手机竞品监控' }
      }
    ]
    pagination.value.total = 1
  } finally {
    tableLoading.value = false
  }
}

const loadCompetitorTasks = async () => {
  try {
    // 调用竞品任务API
    const response = await competitorTaskApi.getTasks()
    if (response.data.success) {
      competitorTasks.value = response.data.data || []
    } else {
      competitorTasks.value = [
        { id: 1, name: '手机竞品监控' },
        { id: 2, name: '笔记本竞品监控' }
      ]
    }
  } catch (error) {
    console.error('加载竞品任务失败:', error)
    competitorTasks.value = [
      { id: 1, name: '手机竞品监控' },
      { id: 2, name: '笔记本竞品监控' }
    ]
  }
}

const loadDataSources = async () => {
  try {
    // TODO: 调用API获取数据源列表
    dataSources.value = [
      { id: 1, name: '综合电商API' },
      { id: 2, name: '京东API' }
    ]
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

const loadCategories = async () => {
  try {
    // TODO: 调用API获取类目列表
    categories.value = [
      { id: 1, name: '手机数码' },
      { id: 2, name: '电脑办公' }
    ]
  } catch (error) {
    console.error('加载类目失败:', error)
  }
}

// 工具方法
const formatPrice = (price) => {
  if (!price) return '0.00'
  return parseFloat(price).toFixed(2)
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatChange = (change) => {
  if (!change) return '-'
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(1)}%`
}

const getChangeClass = (change) => {
  if (!change) return ''
  return change > 0 ? 'positive' : 'negative'
}

const getPromotionTendencyType = (tendency) => {
  const typeMap = {
    'active': 'success',
    'moderate': 'warning',
    'conservative': 'info'
  }
  return typeMap[tendency] || 'info'
}

const getPromotionTendencyLabel = (tendency) => {
  const labelMap = {
    'active': '积极',
    'moderate': '适中',
    'conservative': '保守'
  }
  return labelMap[tendency] || '未知'
}

const getDiscountRateClass = (rate) => {
  if (!rate) return ''
  if (rate > 20) return 'high-discount'
  if (rate > 10) return 'medium-discount'
  return 'low-discount'
}

const getDeviationRateClass = (rate) => {
  if (!rate) return ''
  const absRate = Math.abs(rate)
  if (absRate > 15) return 'high-deviation'
  if (absRate > 5) return 'medium-deviation'
  return 'low-deviation'
}

// 事件处理
const handleSearch = () => {
  pagination.value.current_page = 1
  loadCompetitorData()
}

const handleSelectionChange = (selection) => {
  selectedProducts.value = selection
}

const handleSortChange = ({ prop, order }) => {
  searchForm.sort_by = prop
  searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'
  loadCompetitorData()
}

const handleSizeChange = (size) => {
  pagination.value.per_page = size
  loadCompetitorData()
}

const handleCurrentChange = (page) => {
  pagination.value.current_page = page
  loadCompetitorData()
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStatistics(),
      loadCoreMetrics(),
      loadCompetitorData(),
      loadCompetitorTasks(),
      loadDataSources(),
      loadCategories()
    ])
  } finally {
    loading.value = false
  }
}

const exportData = async () => {
  try {
    exportLoading.value = true

    const response = await competitorAnalyticsApi.exportAnalysisReport({
      task_id: filters.task_id,
      date_range: dateRange.value,
      format: 'excel'
    })

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `竞品分析报告_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewCompetitorDetail = (row) => {
  ElMessage.info(`查看竞品详情: ${row.product_name}`)
}

const loadPriceTrendChart = async () => {
  if (!priceTrendChart.value) return

  chartsLoading.value.priceTrend = true
  try {
    // 获取选中的任务ID
    const taskId = searchForm.task_id || (competitorTasks.value[0]?.id)
    if (!taskId) {
      ElMessage.warning('请先选择监控任务')
      return
    }

    // 转换期间参数
    const days = priceTrendPeriod.value === '7d' ? 7 :
                 priceTrendPeriod.value === '30d' ? 30 : 90

    // 调用API获取价格趋势数据
    const response = await competitorAnalyticsApi.getPriceTrendChart({
      task_id: taskId,
      period: days
    })

    if (response.data.success) {
      const chartData = response.data.data
      renderPriceTrendChart(chartData)
    }
  } catch (error) {
    console.error('加载价格趋势图表失败:', error)
    ElMessage.error('加载价格趋势图表失败')
  } finally {
    chartsLoading.value.priceTrend = false
  }
}

const renderPriceTrendChart = (data) => {
  const chart = echarts.init(priceTrendChart.value)

  const series = data.map(item => ({
    name: item.title,
    type: 'line',
    smooth: true,
    data: item.price_history.map(point => [point.day, point.price]),
    lineStyle: {
      width: 2
    },
    symbol: 'circle',
    symbolSize: 4
  }))

  const option = {
    title: {
      text: '竞品价格趋势对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params) {
        let result = `第${params[0].data[0]}天<br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ¥${param.data[1]}<br/>`
        })
        return result
      }
    },
    legend: {
      top: 30,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '天数',
      nameLocation: 'middle',
      nameGap: 25
    },
    yAxis: {
      type: 'value',
      name: '价格(元)',
      nameLocation: 'middle',
      nameGap: 40,
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: series,
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452']
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

const loadPromotionPieChart = async () => {
  if (!promotionPieChart.value) return

  chartsLoading.value.promotionPie = true
  try {
    const taskId = searchForm.task_id || (competitorTasks.value[0]?.id)
    if (!taskId) return

    const response = await competitorAnalyticsApi.getPromotionDistribution({
      task_id: taskId,
      date_range: 30
    })

    if (response.data.success) {
      renderPromotionPieChart(response.data.data)
    }
  } catch (error) {
    console.error('加载促销分布图表失败:', error)
    ElMessage.error('加载促销分布图表失败')
  } finally {
    chartsLoading.value.promotionPie = false
  }
}

const renderPromotionPieChart = (data) => {
  const chart = echarts.init(promotionPieChart.value)

  const pieData = Object.entries(data).map(([key, value]) => ({
    name: key,
    value: value.count,
    percentage: value.percentage
  }))

  const option = {
    title: {
      text: '促销类型分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '促销类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ],
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452']
  }

  chart.setOption(option)

  window.addEventListener('resize', () => {
    chart.resize()
  })
}

const loadCategoryHeatmap = async () => {
  if (!categoryHeatmapChart.value) return

  chartsLoading.value.categoryHeatmap = true
  try {
    const taskId = searchForm.task_id || (competitorTasks.value[0]?.id)
    if (!taskId) return

    const response = await competitorAnalyticsApi.getCategoryPriceHeatmap({
      task_id: taskId,
      category_id: selectedCategory.value,
      date_range: 30
    })

    if (response.data.success) {
      renderCategoryHeatmap(response.data.data)
    }
  } catch (error) {
    console.error('加载类目热力图失败:', error)
    ElMessage.error('加载类目热力图失败')
  } finally {
    chartsLoading.value.categoryHeatmap = false
  }
}

const renderCategoryHeatmap = (data) => {
  const chart = echarts.init(categoryHeatmapChart.value)

  // 处理热力图数据
  const heatmapData = data.heatmap_data || []
  const categories = data.categories || []
  const priceRanges = data.price_ranges || []

  const option = {
    title: {
      text: '类目价格带分布热力图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      position: 'top',
      formatter: function(params) {
        const category = categories[params.data[0]]
        const priceRange = priceRanges[params.data[1]]
        const count = params.data[2]
        return `${category}<br/>${priceRange}<br/>商品数量: ${count}`
      }
    },
    grid: {
      height: '60%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: categories,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'category',
      data: priceRanges,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: Math.max(...heatmapData.map(item => item[2])),
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '商品数量',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  chart.setOption(option)

  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 生命周期
onMounted(async () => {
  await refreshData()

  // 等待DOM渲染完成后初始化图表
  await nextTick()

  // 初始化图表
  if (priceTrendChart.value) {
    loadPriceTrendChart()
  }
  if (promotionPieChart.value) {
    loadPromotionPieChart()
  }
  if (categoryHeatmapChart.value) {
    loadCategoryHeatmap()
  }
})
</script>

<style scoped>
/* 基础布局 */
.competitor-dashboard {
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  text-align: center;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}

/* 核心指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
}

.metric-icon.promotion {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.metric-icon.discount {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.metric-icon.intensity {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.metric-icon.deviation {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #67c23a;
}

.metric-change.negative {
  color: #f56c6c;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: 12px;
}

.chart-content {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container-large {
  width: 100%;
  height: 400px;
}

/* 数据表格 */
.data-table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.product-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.meta-item {
  font-size: 12px;
  color: #64748b;
}

.price-text {
  font-weight: 600;
  color: #2c3e50;
}

.high-discount {
  color: #f56c6c;
  font-weight: 600;
}

.medium-discount {
  color: #e6a23c;
  font-weight: 500;
}

.low-discount {
  color: #67c23a;
}

.high-deviation {
  color: #f56c6c;
  font-weight: 600;
}

.medium-deviation {
  color: #e6a23c;
  font-weight: 500;
}

.low-deviation {
  color: #67c23a;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.trend-stable {
  color: #909399;
}

.trend-value {
  font-size: 12px;
  color: #64748b;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}
</style>
