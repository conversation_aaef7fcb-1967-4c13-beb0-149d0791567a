<template>
  <div class="channel-price-alerts">
    <el-card class="page-header-card">
      <div class="page-header">
        <el-page-header content="渠道价格预警中心" @back="$router.back()" />
        <div class="header-actions">
          <el-button type="primary" :icon="Plus" @click="handleCreateRule">创建预警规则</el-button>
        </div>
      </div>
      <p class="page-description">
        在这里管理和监控渠道价格的预警规则。您可以创建、编辑、查看和删除预警规则，确保及时响应市场价格波动。
      </p>
    </el-card>

    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.activeRules }}</div>
              <div class="stat-label">活跃规则</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon today">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.todayAlerts }}</div>
              <div class="stat-label">今日预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon unread">
              <el-icon><Message /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.unreadAlerts }}</div>
              <div class="stat-label">未读预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalAlerts }}</div>
              <div class="stat-label">总预警数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-tabs v-model="activeTab" class="alert-tabs">
      <el-tab-pane label="预警规则" name="rules">
        <el-card class="rules-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">预警规则列表</span>
              <div class="card-actions">
                 <el-input
                    v-model="rulesSearchQuery"
                    placeholder="搜索规则名称或描述..."
                    :prefix-icon="Search"
                    clearable
                    style="width: 250px; margin-right: 10px;"
                    @keyup.enter="loadRules"
                    @clear="loadRules"
                  />
                  <el-button :icon="Search" @click="loadRules">搜索</el-button>
              </div>
            </div>
          </template>
          <el-table :data="filteredRules" style="width: 100%" v-loading="loading" stripe>
            <el-table-column prop="name" label="规则名称" min-width="180">
                <template #default="{ row }">
                    <div class="rule-name-cell">
                        <span class="rule-name-text">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="rule-description-cell">
                        <span class="rule-description-text">{{ row.description || '暂无描述' }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="rule_type" label="规则类型" width="200">
              <template #default="{ row }">
                <div class="rule-type-cell" v-if="Array.isArray(row.rule_type)">
                  <el-tag
                    v-for="type in row.rule_type"
                    :key="type"
                    :type="getRuleTypeTagType(type)"
                    size="small"
                    class="rule-type-tag"
                  >
                    {{ getRuleTypeLabel(type) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.status"
                  :active-value="true"
                  :inactive-value="false"
                  @change="updateRuleStatus(row)"
                  :loading="row.statusLoading"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column prop="last_triggered_at" label="上次触发" width="140" align="center">
                <template #default="{ row }">
                    <div class="trigger-time-cell">
                        <span class="trigger-time-text">
                            {{ row.last_triggered_at ? formatDateTime(row.last_triggered_at) : '从未' }}
                        </span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                    <el-button size="small" :icon="View" @click="handleViewRule(row)" plain>详情</el-button>
                    <el-button size="small" type="primary" :icon="Edit" @click="handleEditRule(row)" plain>编辑</el-button>
                    <el-popconfirm title="确定删除这条规则吗？" @confirm="deleteRule(row.id)">
                      <template #reference>
                        <el-button size="small" type="danger" :icon="Delete" plain>删除</el-button>
                      </template>
                    </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="pagination.total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            :page-sizes="[10, 20, 50]"
            :current-page="pagination.currentPage"
            :page-size="pagination.pageSize"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination-container"
          />
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="预警历史" name="history">
        <!-- AlertHistory component -->
      </el-tab-pane>
    </el-tabs>

    <!-- Create/Edit Rule Dialog -->
    <el-dialog v-model="showRuleDialog" :title="dialogTitle" width="45%" @close="resetForm">
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="140px" class="rule-form">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="例如：五一促销价格监控"></el-input>
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" placeholder="对此规则的详细说明"></el-input>
        </el-form-item>
        
        <el-form-item label="规则类型" prop="rule_type">
            <el-checkbox-group v-model="ruleForm.rule_type">
                <el-checkbox v-for="(label, type) in ruleTypeOptions" :key="type" :label="type">
                    {{ label }}
                </el-checkbox>
            </el-checkbox-group>
        </el-form-item>

        <div class="conditions-panel">
            <!-- 上下架状态变更 -->
            <div v-if="isTypeSelected('listing_status_change')" class="condition-group static-info">
                <el-alert title="[上下架状态] 规则说明" type="info" :closable="false" show-icon>
                    当监控的商品发生下架事件时，此规则将被触发。无需额外配置。
                </el-alert>
            </div>
            
            <!-- 促销价偏离率 -->
            <div class="condition-group">
                <div class="condition-header">
                    <el-icon><PriceTag /></el-icon>
                    <span>促销价偏离率</span>
                </div>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="操作符" :prop="isTypeSelected('promotion_price_deviation') ? 'conditions.promotion_price_deviation.operator' : ''">
                            <el-select v-model="ruleForm.conditions.promotion_price_deviation.operator" placeholder="操作符" :disabled="!isTypeSelected('promotion_price_deviation')">
                                <el-option v-for="(label, value) in operatorOptions" :key="value" :label="label" :value="value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="阈值(%)" :prop="isTypeSelected('promotion_price_deviation') ? 'conditions.promotion_price_deviation.threshold' : ''">
                            <el-input-number v-model="ruleForm.conditions.promotion_price_deviation.threshold" :min="0" :max="1000" controls-position="right" :disabled="!isTypeSelected('promotion_price_deviation')"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <!-- 渠道价格偏离率 -->
            <div class="condition-group">
                <div class="condition-header">
                    <el-icon><Shop /></el-icon>
                    <span>渠道价格偏离率</span>
                </div>
                 <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="操作符" :prop="isTypeSelected('channel_price_deviation') ? 'conditions.channel_price_deviation.operator' : ''">
                            <el-select v-model="ruleForm.conditions.channel_price_deviation.operator" placeholder="操作符" :disabled="!isTypeSelected('channel_price_deviation')">
                                <el-option v-for="(label, value) in operatorOptions" :key="value" :label="label" :value="value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="阈值(%)" :prop="isTypeSelected('channel_price_deviation') ? 'conditions.channel_price_deviation.threshold' : ''">
                            <el-input-number v-model="ruleForm.conditions.channel_price_deviation.threshold" :min="0" :max="1000" controls-position="right" :disabled="!isTypeSelected('channel_price_deviation')"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </div>


        <el-form-item label="严重级别" prop="severity">
          <el-select v-model="ruleForm.severity" placeholder="选择严重级别">
            <el-option v-for="(label, value) in severityOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="ruleForm.priority">
            <el-radio-button v-for="(label, value) in priorityOptions" :key="value" :label="value">{{ label }}</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="通知方式" prop="notification_method">
          <el-checkbox-group v-model="ruleForm.notification_method">
            <el-checkbox v-for="(label, value) in notificationMethodOptions" :key="value" :label="value">{{ label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="初始状态" prop="status">
          <el-switch v-model="ruleForm.status" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRuleDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveRule" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
    
    <!-- Rule Detail Dialog -->
    <el-dialog v-model="showDetailDialog" title="预警规则详情" width="40%">
      <div v-if="selectedRuleDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="规则名称">{{ selectedRuleDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedRuleDetail.status ? 'success' : 'info'">{{ selectedRuleDetail.status ? '已启用' : '已禁用' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="规则描述" :span="2">{{ selectedRuleDetail.description || '无' }}</el-descriptions-item>
          <el-descriptions-item label="触发条件" :span="2">
             <div v-for="type in selectedRuleDetail.rule_type" :key="type" class="detail-condition-item">
                <el-tag size="small" style="margin-right: 8px;">{{ getRuleTypeLabel(type) }}</el-tag>
                <span v-if="type === 'listing_status_change'">商品下架时预警</span>
                <span v-else>
                  当 {{ getRuleTypeLabel(type) }}
                  <strong>{{ getOperatorLabel(selectedRuleDetail.conditions[type]?.operator) }}</strong>
                  <strong>{{ selectedRuleDetail.conditions[type]?.threshold }}%</strong>
                </span>
             </div>
          </el-descriptions-item>
          <el-descriptions-item label="严重级别">{{ severityOptions[selectedRuleDetail.severity] }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ priorityOptions[selectedRuleDetail.priority] }}</el-descriptions-item>
          <el-descriptions-item label="通知方式" :span="2">
            <el-tag v-for="method in selectedRuleDetail.notification_method" :key="method" style="margin-right: 5px;">
              {{ notificationMethodOptions[method] }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedRuleDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(selectedRuleDetail.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, ElPopconfirm, ElTable, ElForm } from 'element-plus';
import apiClient from '@/api/apiClient';
import alertsApi from '@/api/alerts';
import { Plus, Edit, Delete, View, Search, PriceTag, Shop } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';

// --- Constants ---
const ruleTypeOptions = {
  listing_status_change: '上下架状态',
  promotion_price_deviation: '促销价偏离率',
  channel_price_deviation: '渠道价格偏离率',
};
const operatorOptions = {
  '>': '大于',
  '<': '小于',
  '>=': '大于等于',
  '<=': '小于等于',
  '==': '等于',
};
const severityOptions = {
  critical: '严重',
  high: '高',
  medium: '中',
  low: '低',
};
const priorityOptions = {
  urgent: '紧急',
  high: '高',
  medium: '中',
  low: '低',
};
const notificationMethodOptions = {
  email: '邮件',
  system: '系统通知',
};

// --- Refs and Reactives ---
const activeTab = ref('rules');
const rules = ref<any[]>([]);
const loading = ref(false);
const saving = ref(false);
const stats = reactive({
  activeRules: 0,
  todayAlerts: 0,
  unreadAlerts: 0,
  totalAlerts: 0,
});
const rulesSearchQuery = ref('');
const showRuleDialog = ref(false);
const showDetailDialog = ref(false);
const dialogMode = ref<'create' | 'edit'>('create');
const selectedRuleDetail = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const createEmptyRuleForm = () => ({
  id: null,
  name: '',
  description: '',
  rule_type: [],
  conditions: {
    promotion_price_deviation: { operator: '>', threshold: 10 },
    channel_price_deviation: { operator: '>', threshold: 10 },
  },
  severity: 'medium',
  priority: 'medium',
  notification_method: ['system'],
  status: true,
});

const ruleForm = reactive(createEmptyRuleForm());

// --- Computed Properties ---
const dialogTitle = computed(() => (dialogMode.value === 'create' ? '创建预警规则' : '编辑预警规则'));
const filteredRules = computed(() => {
    return rules.value; // Search is now handled by backend
});
const ruleRules = computed<FormRules>(() => {
    const isPromoSelected = ruleForm.rule_type.includes('promotion_price_deviation');
    const isChannelSelected = ruleForm.rule_type.includes('channel_price_deviation');

    return {
        name: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
        rule_type: [{ required: true, type: 'array', min: 1, message: '请至少选择一个规则类型', trigger: 'change' }],
        'conditions.promotion_price_deviation.operator': [
            { required: isPromoSelected, message: '操作符不能为空', trigger: 'change' }
        ],
        'conditions.promotion_price_deviation.threshold': [
            { required: isPromoSelected, message: '阈值不能为空', trigger: 'blur' },
            { type: 'number', message: '阈值必须为数字', trigger: 'blur' }
        ],
        'conditions.channel_price_deviation.operator': [
            { required: isChannelSelected, message: '操作符不能为空', trigger: 'change' }
        ],
        'conditions.channel_price_deviation.threshold': [
            { required: isChannelSelected, message: '阈值不能为空', trigger: 'blur' },
            { type: 'number', message: '阈值必须为数字', trigger: 'blur' }
        ],
        severity: [{ required: true, message: '请选择严重级别', trigger: 'change' }],
        notification_method: [{ required: true, type: 'array', min: 1, message: '请至少选择一种通知方式', trigger: 'change' }],
    };
});

// --- Methods ---
const loadStats = async () => {
  try {
    const response = await alertsApi.getStats();
    Object.assign(stats, response);
  } catch (error) {
    console.error('Failed to load stats:', error);
    ElMessage.error('加载统计数据失败');
  }
};

const loadRules = async () => {
  loading.value = true;
  try {
    const response = await alertsApi.getAlertRules({
      page: pagination.currentPage,
      per_page: pagination.pageSize,
      search: rulesSearchQuery.value,
    });
    // 后端返回格式: { success: true, data: $paginatedRules, message: '' }
    // $paginatedRules 是 Laravel 分页对象，包含 data 和其他分页信息
    const paginatedData = response.data;
    rules.value = paginatedData.data.map(rule => ({ ...rule, statusLoading: false }));
    pagination.total = paginatedData.total;
  } catch (error) {
    console.error('Failed to load rules:', error);
    ElMessage.error('加载预警规则失败');
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  loadRules();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  loadRules();
};

const resetForm = () => {
  Object.assign(ruleForm, createEmptyRuleForm());
  ruleFormRef.value?.clearValidate();
};

const handleCreateRule = () => {
  resetForm();
  dialogMode.value = 'create';
  showRuleDialog.value = true;
};

const handleEditRule = (rule: any) => {
  resetForm();
  dialogMode.value = 'edit';
  
  // Deep copy and prepare form data
  const formData = JSON.parse(JSON.stringify(rule));
  const emptyForm = createEmptyRuleForm();
  
  // Ensure all condition objects exist
  formData.conditions = {
      ...emptyForm.conditions,
      ...(formData.conditions || {})
  };

  Object.assign(ruleForm, formData);
  showRuleDialog.value = true;
};

const handleViewRule = (rule: any) => {
    selectedRuleDetail.value = rule;
    showDetailDialog.value = true;
};

const handleSaveRule = async () => {
  if (!ruleFormRef.value) return;
  await ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true;
      try {
        const payload = JSON.parse(JSON.stringify(ruleForm));

        // Clean up conditions that are not selected
        Object.keys(payload.conditions).forEach(key => {
            if(!payload.rule_type.includes(key)) {
                delete payload.conditions[key];
            }
        });
        
        if (dialogMode.value === 'create') {
          await alertsApi.createAlertRule(payload);
          ElMessage.success('规则创建成功');
        } else {
          await alertsApi.updateAlertRule(ruleForm.id, payload);
          ElMessage.success('规则更新成功');
        }
        showRuleDialog.value = false;
        loadRules();
      } catch (error: any) {
        console.error('Failed to save rule:', error);
        ElMessage.error(error.response?.data?.message || '保存规则失败');
      } finally {
        saving.value = false;
      }
    } else {
      ElMessage.error('请检查表单输入');
    }
  });
};

const deleteRule = async (id: number) => {
  try {
    await alertsApi.deleteAlertRule(id);
    ElMessage.success('规则删除成功');
    loadRules();
  } catch (error) {
    console.error('Failed to delete rule:', error);
    ElMessage.error('删除规则失败');
  }
};

const updateRuleStatus = async (rule: any) => {
  rule.statusLoading = true;
  try {
    // 使用现有的API客户端，因为alerts.js中没有更新状态的方法
    await apiClient.put(`/alert-rules/${rule.id}/status`, { status: rule.status });
    ElMessage.success('状态更新成功');
  } catch (error) {
    console.error('Failed to update status:', error);
    ElMessage.error('状态更新失败');
    rule.status = !rule.status; // Revert on failure
  } finally {
    rule.statusLoading = false;
  }
};

// Helper and Formatting functions
const getRuleTypeLabel = (type: string) => ruleTypeOptions[type] || type;
const getRuleTypeTagType = (type: string) => {
    const tagTypes = {
        'promotion_price_deviation': 'warning',
        'channel_price_deviation': 'danger',
        'listing_status_change': 'info'
    };
    return tagTypes[type] || 'primary';
};
const getOperatorLabel = (op: string) => operatorOptions[op] || op;
const isTypeSelected = (type: string) => ruleForm.rule_type.includes(type);
const formatDateTime = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('zh-CN', { hour12: false });
};


onMounted(() => {
  loadRules();
  loadStats();
});
</script>

<style scoped lang="scss">
.channel-price-alerts {
  padding: 20px;
}

.page-header-card {
  margin-bottom: 20px;
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .page-description {
    margin-top: 10px;
    font-size: 14px;
    color: #606266;
  }
}

.stats-cards {
  margin-bottom: 20px;
}

.alert-tabs {
  .rules-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .card-title {
        font-size: 16px;
        font-weight: bold;
      }
      .card-actions {
        display: flex;
        align-items: center;
      }
    }

    // 表格样式优化
    .rule-name-cell {
      .rule-name-text {
        font-weight: 500;
        color: #303133;
      }
    }

    .rule-description-cell {
      .rule-description-text {
        color: #606266;
        font-size: 13px;
        line-height: 1.4;
      }
    }

    .rule-type-cell {
      .rule-type-tag {
        margin-right: 4px;
        margin-bottom: 2px;
      }
    }

    .trigger-time-cell {
      .trigger-time-text {
        font-size: 12px;
        color: #909399;
      }
    }

    .action-buttons {
      display: flex;
      gap: 4px;
      justify-content: center;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

.rule-name-text {
    font-weight: 500;
}

.rule-type-tag {
    margin-right: 5px;
    margin-bottom: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.rule-form {
  .conditions-panel {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fafafa;
  }
  .condition-group {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e4e7ed;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
  .condition-header {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    color: #303133;
    .el-icon {
        margin-right: 6px;
    }
  }
  .static-info {
    border: none;
    padding: 0;
  }
}

.detail-condition-item {
    margin-bottom: 8px;
}
</style> 